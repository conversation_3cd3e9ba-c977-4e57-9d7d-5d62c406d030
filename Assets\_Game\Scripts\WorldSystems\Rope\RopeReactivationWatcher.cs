using UnityEngine;

// Watches rope endpoints for motion and reactivates the dynamic rope when needed
[DisallowMultipleComponent]
public class RopeReactivationWatcher : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private CableComponent cable;
    [SerializeField] private Transform startPoint;
    [SerializeField] private Transform endPoint;
    [SerializeField] private Rigidbody startRigidbody;
    [SerializeField] private Rigidbody endRigidbody;
    [SerializeField] private GameObject staticMeshObject;

    [Header("Reactivation Triggers")]
    [Tooltip("Distance an endpoint must move from the frozen pose to trigger reactivation")]
    [SerializeField] private float positionThreshold = 0.05f;
    [Tooltip("Rigidbody velocity magnitude that will trigger reactivation when it wakes/moves")]
    [SerializeField] private float velocityThreshold = 0.1f;
    [Tooltip("Cooldown after freezing to avoid immediate reactivation due to settling jitter")]
    [SerializeField] private float freezeCooldownSeconds = 0.25f;

    [Header("Reactivation Settings")]
    [Tooltip("Extra slack applied when reactivating to avoid instant break if endpoints moved farther apart")]
    [SerializeField] private float reactivationSlackMultiplier = 1.0f;
    [Toolt<PERSON>("Rotation delta in degrees that will trigger reactivation (captures devices that rotate in place)")] 
    [SerializeField] private float rotationThresholdDegrees = 2f;

    [Header("Arming After Load")]
    [Tooltip("Seconds both endpoints must be settled before reactivation is armed (useful after scene load)")]
    [SerializeField] private float armingStableSeconds = 1.0f;
    [Tooltip("Velocity under which an endpoint rigidbody is considered settled for arming")] 
    [SerializeField] private float armingVelocityThreshold = 0.05f;

    private bool isFrozen = false;
    [Header("On Break")]
    [SerializeField] private string breakDropItemName = "Rope";
    [SerializeField] private int breakDropQuantity = 1;
    [SerializeField] private bool removePersistenceOnBreak = true;

    private bool hasBeenReactivated = false;

    private Vector3 frozenStartPos;
    private Vector3 frozenEndPos;
    private Quaternion frozenStartRot;
    private Quaternion frozenEndRot;
    private float freezeTime;
    private bool isArmed = true;
    private float armStartTime = -1f;
    private Vector3 lastObservedStartPos;
    private Vector3 lastObservedEndPos;

    public void Init(CableComponent cableComponent,
                     GameObject staticGO,
                     float posThreshold,
                     float velThreshold)
    {
        // Back-compat: default to arming immediately for runtime-captured ropes
        Init(cableComponent, staticGO, posThreshold, velThreshold, true);
    }

    public void Init(CableComponent cableComponent,
                     GameObject staticGO,
                     float posThreshold,
                     float velThreshold,
                     bool armImmediately)
    {
        cable = cableComponent != null ? cableComponent : GetComponent<CableComponent>();

        if (cable == null)
        {
            Debug.LogError("RopeReactivationWatcher requires a CableComponent");
            enabled = false;
            return;
        }

        startPoint = cable.StartPoint;
        endPoint = cable.EndPoint;
        startRigidbody = FindRigidbodyForAnchor(startPoint);
        endRigidbody = FindRigidbodyForAnchor(endPoint);

        positionThreshold = Mathf.Max(0.001f, posThreshold);
        velocityThreshold = Mathf.Max(0f, velThreshold);
        SubscribeBreakEvent();
        FreezeWithStatic(staticGO);
        isArmed = armImmediately;
        armStartTime = -1f;
        lastObservedStartPos = startPoint != null ? startPoint.position : Vector3.zero;
        lastObservedEndPos = endPoint != null ? endPoint.position : Vector3.zero;
    }

    public void FreezeWithStatic(GameObject staticGO)
    {
        staticMeshObject = staticGO;
        if (cable == null)
            cable = GetComponent<CableComponent>();

        if (cable == null)
            return;

        frozenStartPos = startPoint != null ? startPoint.position : Vector3.zero;
        frozenEndPos = endPoint != null ? endPoint.position : Vector3.zero;
        frozenStartRot = startPoint != null ? startPoint.rotation : Quaternion.identity;
        frozenEndRot = endPoint != null ? endPoint.rotation : Quaternion.identity;

        // Hide dynamic renderers and stop sim
        SetDynamicRenderersEnabled(false);
        cable.enabled = false;

        isFrozen = true;
        freezeTime = Time.time;

        // Ensure any renderers created later (e.g., in Start) get disabled too
        StartCoroutine(DisableRenderersNextFrame());
    }

    private void OnEnable()
    {
        SubscribeBreakEvent();
    }

    private void OnDisable()
    {
        UnsubscribeBreakEvent();
    }

    private void SubscribeBreakEvent()
    {
        if (cable == null) cable = GetComponent<CableComponent>();
        if (cable != null)
        {
            // Avoid double-subscribe
            UnsubscribeBreakEvent();
            cable.OnRopeBreak += HandleCableBreak;
        }
    }

    private void UnsubscribeBreakEvent()
    {
        if (cable != null)
        {
            cable.OnRopeBreak -= HandleCableBreak;
        }
    }

    private void HandleCableBreak(CableComponent broken, Vector3 breakPoint)
    {
        // Drop rope item at break point
        try
        {
            var item = ItemDatabase.GetItemByName(breakDropItemName);
            var dropper = GameObject.FindFirstObjectByType<InvItemDropping>();
            if (item != null && dropper != null)
            {
                var dropped = dropper.DropItem(item, Mathf.Max(1, breakDropQuantity));
                if (dropped != null)
                {
                    dropped.transform.position = breakPoint;
                    var rb = dropped.GetComponent<Rigidbody>();
                    if (rb != null)
                    {
                        rb.linearVelocity = Vector3.zero;
                        rb.angularVelocity = Vector3.zero;
                    }
                }
            }
        }
        catch { }

        // Remove from power system if registered
        try { PowerSystem.Instance?.RemoveCableConnection(broken.gameObject); } catch { }

        // Remove static mesh and persistence record
        try
        {
            var capture = GetComponent<RopeStaticMeshCapture>();
            // For loaded ropes that haven't been reactivated, preserve persistence to prevent accidental data loss
            // For loaded ropes that have been reactivated, allow physics breaks to clear persistence
            bool shouldRemovePersistence = removePersistenceOnBreak || hasBeenReactivated;
            if (shouldRemovePersistence)
            {
                capture?.ClearSavedState();
            }

            if (staticMeshObject != null)
            {
                if (Application.isPlaying)
                    Destroy(staticMeshObject);
                else
                    DestroyImmediate(staticMeshObject);
                staticMeshObject = null;
            }
        }
        catch { }

        // Immediately hide remaining visuals and destroy the broken rope object
        try
        {
            var rends = broken.GetComponentsInChildren<Renderer>(true);
            foreach (var r in rends) if (r != null) r.enabled = false;
            var cols = broken.GetComponentsInChildren<Collider>(true);
            foreach (var c in cols) if (c != null) c.enabled = false;
        }
        catch { }

        try { Destroy(broken.gameObject); } catch { }
    }

    private void Update()
    {
        if (!isFrozen)
            return;

        // After load: wait until endpoints are settled for a while before arming reactivation
        if (!isArmed)
        {
            bool aSettled = IsAnchorSettled(startPoint, startRigidbody);
            bool bSettled = IsAnchorSettled(endPoint, endRigidbody);

            if (aSettled && bSettled)
            {
                if (armStartTime < 0f) armStartTime = Time.time;
                if (Time.time - armStartTime >= armingStableSeconds)
                {
                    // Arm without resetting the frozen baseline so pre-arm motion is still detected
                    freezeTime = Time.time;
                    isArmed = true;
                }
            }
            else
            {
                armStartTime = -1f;
            }

            return; // don't reactivate until armed
        }

        // Avoid immediate reactivation due to tiny jitter right after freezing
        if (Time.time - freezeTime < freezeCooldownSeconds)
            return;

        if (HasEndpointMoved() || HasEndpointRotated() || HasRigidbodyMotion())
        {
            Reactivate();
        }
    }

    private bool IsAnchorSettled(Transform anchor, Rigidbody rb)
    {
        if (anchor == null) return true;
        if (rb != null)
        {
            if (rb.IsSleeping()) return true;
            return rb.linearVelocity.sqrMagnitude <= armingVelocityThreshold * armingVelocityThreshold;
        }
        // Fallback for non-RB anchors: consider settled if barely moving relative to last observation
        Vector3 current = anchor.position;
        Vector3 last = (anchor == startPoint) ? lastObservedStartPos : lastObservedEndPos;
        bool settled = Vector3.Distance(current, last) <= positionThreshold * 0.5f;
        if (anchor == startPoint) lastObservedStartPos = current; else lastObservedEndPos = current;
        return settled;
    }

    private bool HasEndpointMoved()
    {
        if (startPoint != null && Vector3.Distance(startPoint.position, frozenStartPos) > positionThreshold)
            return true;
        if (endPoint != null && Vector3.Distance(endPoint.position, frozenEndPos) > positionThreshold)
            return true;
        return false;
    }

    private bool HasEndpointRotated()
    {
        if (rotationThresholdDegrees <= 0f) return false;
        if (startPoint != null && Quaternion.Angle(startPoint.rotation, frozenStartRot) > rotationThresholdDegrees)
            return true;
        if (endPoint != null && Quaternion.Angle(endPoint.rotation, frozenEndRot) > rotationThresholdDegrees)
            return true;
        return false;
    }

    private bool HasRigidbodyMotion()
    {
        if (startRigidbody != null)
        {
            // If it wakes and is moving, treat as a trigger
            if (!startRigidbody.IsSleeping() && startRigidbody.linearVelocity.sqrMagnitude > velocityThreshold * velocityThreshold)
                return true;
        }
        if (endRigidbody != null)
        {
            if (!endRigidbody.IsSleeping() && endRigidbody.linearVelocity.sqrMagnitude > velocityThreshold * velocityThreshold)
                return true;
        }
        return false;
    }

    private Rigidbody FindRigidbodyForAnchor(Transform anchor)
    {
        if (anchor == null)
            return null;
        // In placement, the anchor is often a child of a rigidbody
        var rb = anchor.GetComponent<Rigidbody>();
        if (rb != null) return rb;
        return anchor.GetComponentInParent<Rigidbody>();
    }

    public void Reactivate()
    {
        // Mark as having been reactivated (for break persistence logic)
        hasBeenReactivated = true;

        // Mark loaded ropes as needing recapture
        var capture = GetComponent<RopeStaticMeshCapture>();
        capture?.MarkNeedsRecapture();

        // Remove static mesh representation
        if (staticMeshObject != null)
        {
            if (Application.isPlaying)
                Destroy(staticMeshObject);
            else
                DestroyImmediate(staticMeshObject);
            staticMeshObject = null;
        }

        // Re-enable dynamic rope
        if (cable != null)
        {
            // Ensure cable length fits current endpoints with controlled slack (do not grow monotonically)
            if (startPoint != null && endPoint != null)
            {
                float dist = Vector3.Distance(startPoint.position, endPoint.position);
                float slack = Mathf.Max(1f, reactivationSlackMultiplier);
                float targetLen = dist * slack;
                CableAdapter.TrySetCableLength(cable, targetLen);
            }
            cable.enabled = true;
            cable.ReinitializeCable();
        }
        SetDynamicRenderersEnabled(true);
        isFrozen = false;
    }

    // Public helpers for external systems (e.g., StaticRopeInteractable)
    public bool IsWatching(GameObject staticObj)
    {
        return staticObj != null && staticMeshObject == staticObj;
    }

    public void DetachFromStatic()
    {
        // Prevent this watcher from destroying the static mesh during cleanup
        staticMeshObject = null;
        // Stop monitoring/reactivating while an interaction is in progress
        enabled = false;
    }

    private void SetDynamicRenderersEnabled(bool enabled)
    {
        // Enable/disable all MeshRenderers under the dynamic rope object
        var renderers = GetComponentsInChildren<MeshRenderer>(true);
        foreach (var r in renderers)
        {
            // Skip the static mesh object if it's a child (typically it's not)
            if (staticMeshObject != null && r != null && r.gameObject == staticMeshObject)
                continue;
            r.enabled = enabled;
        }
    }

    private System.Collections.IEnumerator DisableRenderersNextFrame()
    {
        yield return null;
        if (isFrozen)
        {
            SetDynamicRenderersEnabled(false);
        }
    }

    public bool IsFrozen => isFrozen;

    private void OnDestroy()
    {
        // Ensure we don't leave orphaned static meshes or saved states
        if (staticMeshObject != null)
        {
            if (Application.isPlaying)
                Destroy(staticMeshObject);
            else
                DestroyImmediate(staticMeshObject);
            staticMeshObject = null;
        }
        // Do not clear persistence here; the rope state should persist across sessions
    }
}
