using System;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

[System.Serializable]
public class RopeStateData
{
    public Vector3[] positions;
    public float radius;
    public int sides;
    public string captureTime;
    public string ropeName;
    
    public RopeStateData(Vector3[] pos, float rad, int s, string name)
    {
        positions = pos;
        radius = rad;
        sides = s;
        captureTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        ropeName = name;
    }
}

[RequireComponent(typeof(CableComponent))]
public class RopeStaticMeshCapture : MonoBehaviour
{
    [Header("Capture Settings")]
    [SerializeField] private KeyCode captureKey = KeyCode.Return;
    [SerializeField] private bool captureOnKey = true;
    [SerializeField] private string meshNamePrefix = "CapturedRope";
    [SerializeField] private bool requireSettled = true;
    [SerializeField] private bool destroyDynamicAfterCapture = true;
    
    [Header("Reactivation")]
    [SerializeField] private bool reactivateOnEndpointsMotion = true;
    [SerializeField] private float reactivationPositionThreshold = 0.05f;
    [SerializeField] private float reactivationVelocityThreshold = 0.1f;
    
    [Header("Capture Control")]
    [SerializeField] private float captureCooldownSeconds = 0.5f;
    [SerializeField] private string customPrefsKey = ""; // Optional override key (e.g., CableState|idA|idB)
    
    [Header("Settling Detection")]
    [SerializeField] private float settleVelocityThreshold = 0.01f;
    [SerializeField] private float settleTimeRequired = 2f;
    [SerializeField] private bool showSettleStatus = true;

    [Header("Mesh Settings")]
    [SerializeField] private int meshSides = 8;
    [SerializeField] private float meshRadius = 0.05f;
    [SerializeField] private bool addEndCaps = true;
    [SerializeField] private bool optimizeMesh = true;
    
    [Header("Saved State")]
    [SerializeField] private RopeStateData savedState;
    [SerializeField] private Mesh lastCapturedMesh;
    [SerializeField] private GameObject currentStaticMesh;

    [Header("Cached Endpoint Data")]
    [SerializeField] private string cachedIdA = null;
    [SerializeField] private string cachedIdB = null;
    [SerializeField] private bool cachedIsCable = false;

    [Header("Loaded Rope Tracking")]
    [SerializeField] private bool isLoadedRope = false;
    [SerializeField] private bool needsRecapture = false;
    
    [Header("Static Mesh Object")]
    [SerializeField] private GameObject staticMeshPrefab;
    [SerializeField] private Material staticMeshMaterial;
    
    [Header("Static Layer")]
    [Tooltip("Layer index used for generated static rope meshes (set to a non-colliding layer).")]
    [SerializeField] private int staticMeshLayer = 8;
    
    private CableComponent cableComponent;
    private List<GameObject> createdStaticMeshes = new List<GameObject>();
    private static bool s_StaticLayerConfigured = false;
    
    // Settling detection state
    private float currentSettleTime = 0f;
    private bool isRopeSettled = false;
    private float lastAverageVelocity = 0f;
    private float lastCaptureTime = -999f;
    
    void Start()
    {
        // Ensure persistence manager exists to receive saved state.
        MachinePersistenceManager.Ensure();
        cableComponent = GetComponent<CableComponent>();
        
        if (cableComponent == null)
        {
            Debug.LogError("RopeStaticMeshCapture requires a CableComponent!");
            enabled = false;
            return;
        }
        
        // Cache endpoint ids early to avoid destruction-order issues on quit
        CacheEndpointIds();

        // Load any saved rope state (no-op; loader handles static meshes)
        LoadSavedState();
    }

    private void OnEnable()
    {
        // Best effort to cache whenever enabled
        if (cableComponent == null) cableComponent = GetComponent<CableComponent>();
        CacheEndpointIds();
    }
    
    void Update()
    {
        // Update settling detection
        if (requireSettled)
        {
            CheckRopeSettled();
        }
        
        if (captureOnKey && Input.GetKeyDown(captureKey))
        {
            CaptureCurrentState();
        }
        
        // Debug keys
        if (Input.GetKeyDown(KeyCode.G) && Input.GetKey(KeyCode.LeftControl))
        {
            if (savedState != null)
            {
                CreateStaticMeshFromSavedState();
            }
        }
        
        if (Input.GetKeyDown(KeyCode.H) && Input.GetKey(KeyCode.LeftControl))
        {
            ClearAllStaticMeshes();
        }
    }

    void OnApplicationQuit()
    {
        // Skip capture for loaded ropes that haven't reactivated
        if (isLoadedRope && !needsRecapture)
        {
            return;
        }

        // Ensure rope is persisted as static on quit even if not fully settled
        try { ForceCaptureNow(); } catch {}
    }
    
    private void CheckRopeSettled()
    {
        if (cableComponent == null) return;
        
        // Get cable particles using helper method
        CableParticle[] particles = GetCableParticles();
        if (particles == null) return;
        
        // Calculate average velocity of all free rope particles
        float totalVelocity = 0f;
        int freeParticleCount = 0;
        
        foreach (var particle in particles)
        {
            if (particle.IsFree()) // Only check free particles (not bound to endpoints)
            {
                totalVelocity += particle.Velocity.magnitude;
                freeParticleCount++;
            }
        }
        
        if (freeParticleCount > 0)
        {
            lastAverageVelocity = totalVelocity / freeParticleCount;
            
            // Check if velocity is below threshold
            if (lastAverageVelocity < settleVelocityThreshold)
            {
                currentSettleTime += Time.deltaTime;
                isRopeSettled = currentSettleTime >= settleTimeRequired;
            }
            else
            {
                currentSettleTime = 0f;
                isRopeSettled = false;
            }
        }
        else
        {
            // If no free particles, consider it settled (shouldn't happen normally)
            isRopeSettled = true;
        }
    }
    
    [ContextMenu("Capture Current Rope State")]
    public void CaptureCurrentState()
    {
        if (cableComponent == null) return;
        
        // Prevent spamming duplicates
        if (Time.time - lastCaptureTime < captureCooldownSeconds)
            return;
        if (currentStaticMesh != null)
            return;
        var watcherCheck = GetComponent<RopeReactivationWatcher>();
        if (watcherCheck != null && watcherCheck.IsFrozen)
            return;
        
        // Check if rope must be settled before capture
        if (requireSettled && !isRopeSettled)
        {
            Debug.LogWarning("Rope must be settled before capture! Wait for it to stop moving.");
            return;
        }
        
        // Refresh cached endpoint ids in case endpoints changed
        CacheEndpointIds();

        // Get positions from cable component
        int segmentCount = GetSegmentCount();
        Vector3[] positions = new Vector3[segmentCount + 1];
        
        for (int i = 0; i <= segmentCount; i++)
        {
            positions[i] = cableComponent.GetPointPosition(i);
        }
        
        // Determine thickness from live cable when available
        float radiusToUse = cableComponent != null ? cableComponent.Radius : meshRadius;
        // Store the state
        savedState = new RopeStateData(
            positions,
            radiusToUse,
            meshSides,
            gameObject.name
        );
        
        // Save to persistent storage
        SaveStateToPrefs();
        
        lastCaptureTime = Time.time;
        
        // Create static mesh immediately (pivot/localized) with captured thickness
        GameObject staticGO = CreateStaticMeshObjectFromPositions(positions, radiusToUse, meshSides);
        if (staticGO != null)
        {
            var mf = staticGO.GetComponent<MeshFilter>();
            if (mf != null) lastCapturedMesh = mf.sharedMesh;
        }

        // If enabled, keep dynamic rope around (frozen) and reactivate if endpoints move
        if (reactivateOnEndpointsMotion)
        {
            var watcher = GetComponent<RopeReactivationWatcher>();
            if (watcher == null) watcher = gameObject.AddComponent<RopeReactivationWatcher>();
            watcher.Init(cableComponent, staticGO, reactivationPositionThreshold, reactivationVelocityThreshold);

            // When reactivation is enabled we keep the dynamic rope; ignore destroyDynamicAfterCapture
        }
        else if (destroyDynamicAfterCapture)
        {
            // Add a small delay to ensure the static mesh is fully created before destroying
            StartCoroutine(DestroyAfterCapture());
        }
    }

    // Force capture regardless of settled status and cooldown (used on quit)
    public void ForceCaptureNow()
    {
        if (cableComponent == null) return;
        if (currentStaticMesh != null) return; // already captured

        // Try to cache endpoint ids before anything gets destroyed
        CacheEndpointIds();

        int segmentCount = GetSegmentCount();
        Vector3[] positions = new Vector3[segmentCount + 1];
        for (int i = 0; i <= segmentCount; i++)
        {
            positions[i] = cableComponent.GetPointPosition(i);
        }

        float radiusToUse = cableComponent != null ? cableComponent.Radius : meshRadius;
        savedState = new RopeStateData(
            positions,
            radiusToUse,
            meshSides,
            gameObject.name
        );

        SaveStateToPrefs();
        lastCaptureTime = Time.time;
        
        GameObject staticGO = CreateStaticMeshObjectFromPositions(positions, radiusToUse, meshSides);
        if (staticGO != null)
        {
            var mf = staticGO.GetComponent<MeshFilter>();
            if (mf != null) lastCapturedMesh = mf.sharedMesh;
        }

        if (reactivateOnEndpointsMotion)
        {
            var watcher = GetComponent<RopeReactivationWatcher>();
            if (watcher == null) watcher = gameObject.AddComponent<RopeReactivationWatcher>();
            watcher.Init(cableComponent, staticGO, reactivationPositionThreshold, reactivationVelocityThreshold);
        }
        else if (destroyDynamicAfterCapture)
        {
            StartCoroutine(DestroyAfterCapture());
        }
    }

    public void MarkNeedsRecapture()
    {
        needsRecapture = true;
    }
    
    private int GetSegmentCount()
    {
        if (cableComponent == null) return 24;
        return cableComponent.GetSegmentCount();
    }

    // Public setter to avoid reflection in external callers
    public void SetCustomPrefsKey(string key)
    {
        customPrefsKey = key ?? string.Empty;
    }
    
    private CableParticle[] GetCableParticles()
    {
        // Helper method to get the private _Points array from CableComponent
        var pointsField = cableComponent.GetType().GetField("_Points",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (pointsField != null)
        {
            return pointsField.GetValue(cableComponent) as CableParticle[];
        }

        return null;
    }

    private System.Collections.IEnumerator DestroyAfterCapture()
    {
        // Wait a frame to ensure static mesh is fully created
        yield return null;
        // Then destroy the dynamic rope
        Destroy(gameObject);
    }

    private Mesh GenerateMeshFromPositions(Vector3[] positions, float radius, int sides)
    {
        if (positions == null || positions.Length < 2)
        {
            Debug.LogError("Need at least 2 positions to generate mesh!");
            return null;
        }

        Mesh mesh = new Mesh();
        mesh.name = $"{meshNamePrefix}_{DateTime.Now:yyyyMMdd_HHmmss}";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Vector2> uvs = new List<Vector2>();

        // Generate tube mesh
        for (int i = 0; i < positions.Length; i++)
        {
            Vector3[] circle = CalculateCircleVertices(positions, i, radius, sides);

            // Add vertices for this ring
            for (int j = 0; j < sides; j++)
            {
                vertices.Add(circle[j]);

                float u = (float)j / (sides - 1);
                float v = (float)i / (positions.Length - 1);
                uvs.Add(new Vector2(u, v));
            }
        }

        // Generate triangles for tube (fixed winding order)
        for (int segment = 0; segment < positions.Length - 1; segment++)
        {
            for (int side = 0; side < sides; side++)
            {
                int current = segment * sides + side;
                int next = current + sides;
                int currentNextSide = (side == sides - 1) ? (segment * sides) : (current + 1);
                int nextNextSide = (side == sides - 1) ? ((segment + 1) * sides) : (next + 1);

                // First triangle (counter-clockwise winding)
                triangles.Add(current);
                triangles.Add(currentNextSide);
                triangles.Add(next);

                // Second triangle (counter-clockwise winding)
                triangles.Add(currentNextSide);
                triangles.Add(nextNextSide);
                triangles.Add(next);
            }
        }

        // Add end caps if requested
        if (addEndCaps)
        {
            AddEndCap(vertices, triangles, uvs, positions[0], positions, 0, radius, sides, true);
            AddEndCap(vertices, triangles, uvs, positions[positions.Length - 1], positions,
                positions.Length - 1, radius, sides, false);
        }

        // Apply to mesh
        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.uv = uvs.ToArray();

        // Recalculate normals and bounds
        mesh.RecalculateNormals();
        mesh.RecalculateBounds();
        mesh.RecalculateTangents();

        #if UNITY_EDITOR
        if (optimizeMesh)
        {
            mesh.Optimize();
        }
        #endif

        return mesh;
    }

    private Vector3[] CalculateCircleVertices(Vector3[] positions, int index, float radius, int sides)
    {
        Vector3 position = positions[index];

        // Calculate forward direction
        Vector3 forward = Vector3.zero;
        if (index > 0)
            forward += (positions[index] - positions[index - 1]).normalized;
        if (index < positions.Length - 1)
            forward += (positions[index + 1] - positions[index]).normalized;

        if (forward.magnitude < 0.001f)
            forward = Vector3.forward;
        else
            forward = forward.normalized;

        // Calculate perpendicular vectors
        Vector3 up = Vector3.up;
        if (Mathf.Abs(Vector3.Dot(forward, up)) > 0.99f)
            up = Vector3.right;

        Vector3 right = Vector3.Cross(forward, up).normalized;
        up = Vector3.Cross(forward, right).normalized;

        // Generate circle vertices
        Vector3[] circle = new Vector3[sides];
        float angleStep = (2f * Mathf.PI) / sides;

        for (int i = 0; i < sides; i++)
        {
            float angle = i * angleStep;
            float x = Mathf.Cos(angle) * radius;
            float y = Mathf.Sin(angle) * radius;
            circle[i] = position + right * x + up * y;
        }

        return circle;
    }

    private void AddEndCap(List<Vector3> vertices, List<int> triangles, List<Vector2> uvs,
        Vector3 center, Vector3[] positions, int posIndex, float radius, int sides, bool isStart)
    {
        // Calculate cap direction
        Vector3 capNormal;
        if (isStart)
        {
            capNormal = (positions[0] - positions[1]).normalized;
        }
        else
        {
            capNormal = (positions[positions.Length - 1] - positions[positions.Length - 2]).normalized;
        }

        // Add center vertex
        int centerIndex = vertices.Count;
        vertices.Add(center);
        uvs.Add(new Vector2(0.5f, 0.5f));

        // Get ring vertices indices
        int ringStartIndex = posIndex * sides;

        // Create triangles for cap (fixed winding order)
        for (int i = 0; i < sides; i++)
        {
            int current = ringStartIndex + i;
            int next = ringStartIndex + ((i + 1) % sides);

            if (isStart)
            {
                // Start cap faces inward (reverse winding)
                triangles.Add(centerIndex);
                triangles.Add(current);
                triangles.Add(next);
            }
            else
            {
                // End cap faces outward (normal winding)
                triangles.Add(centerIndex);
                triangles.Add(next);
                triangles.Add(current);
            }
        }
    }

    private GameObject CreateStaticMeshObjectFromPositions(Vector3[] worldPositions, float radius, int sides)
    {
        if (worldPositions == null || worldPositions.Length < 2) return null;
        if (currentStaticMesh != null) return currentStaticMesh;

        Vector3 pivot = worldPositions[0];
        Vector3[] localPositions = new Vector3[worldPositions.Length];
        for (int i = 0; i < worldPositions.Length; i++)
            localPositions[i] = worldPositions[i] - pivot;

        Mesh mesh = GenerateMeshFromPositions(localPositions, radius, sides);
        if (mesh == null) return null;

        GameObject staticRope = new GameObject($"StaticRope_{createdStaticMeshes.Count}");
        TryConfigureStaticLayer();
        staticRope.layer = staticMeshLayer;
        staticRope.transform.position = pivot;
        createdStaticMeshes.Add(staticRope);

        MeshFilter meshFilter = staticRope.AddComponent<MeshFilter>();
        MeshRenderer meshRenderer = staticRope.AddComponent<MeshRenderer>();
        meshFilter.mesh = mesh;

        if (staticMeshMaterial != null)
        {
            meshRenderer.sharedMaterial = staticMeshMaterial;
        }
        else
        {
            MeshRenderer cableRenderer = GetComponent<MeshRenderer>();
            if (cableRenderer != null && cableRenderer.sharedMaterial != null)
            {
                meshRenderer.sharedMaterial = cableRenderer.sharedMaterial;
            }
        }

        // Prefer per-segment capsule triggers for better interaction. Fallback to a single box if anything fails.
        try
        {
            AddSegmentTriggerColliders(staticRope, localPositions, radius);
        }
        catch
        {
            var box = staticRope.AddComponent<BoxCollider>();
            box.center = mesh.bounds.center; // local bounds
            box.size = mesh.bounds.size;
            box.isTrigger = true;
            box.gameObject.layer = staticMeshLayer;
        }

        var interact = staticRope.AddComponent<StaticRopeInteractable>();
        string key = string.IsNullOrEmpty(customPrefsKey) ? $"RopeState|{gameObject.name}" : customPrefsKey;
        string idA = null, idB = null; bool isCable = false;
        if (cableComponent != null)
        {
            var a = cableComponent.StartPoint; var b = cableComponent.EndPoint;
            if (a != null && b != null)
            {
                var mA = a.GetComponentInParent<Machine>(); var mB = b.GetComponentInParent<Machine>();
                var gA = a.GetComponentInParent<Generator>(); var gB = b.GetComponentInParent<Generator>();
                var monA = a.GetComponentInParent<MachineMonitor>(); var monB = b.GetComponentInParent<MachineMonitor>();

                string sA = mA != null ? $"M:{mA.MachineId}" : gA != null ? $"G:{gA.GeneratorId}" : monA != null ? $"MONID:{monA.MonitorId}" : null;
                string sB = mB != null ? $"M:{mB.MachineId}" : gB != null ? $"G:{gB.GeneratorId}" : monB != null ? $"MONID:{monB.MonitorId}" : null;

                idA = sA; idB = sB;
                isCable = (idA != null && idB != null);
            }
        }
        interact.Configure(key, isCable, idA, idB);
        
        currentStaticMesh = staticRope;
        return staticRope;
    }

    // Creates a chain of capsule trigger colliders along the rope path
    private void AddSegmentTriggerColliders(GameObject root, Vector3[] localPositions, float radius)
    {
        if (localPositions == null || localPositions.Length < 2) return;
        float segRadius = Mathf.Max(0.01f, radius);
        for (int i = 0; i < localPositions.Length - 1; i++)
        {
            Vector3 a = localPositions[i];
            Vector3 b = localPositions[i + 1];
            Vector3 mid = (a + b) * 0.5f;
            Vector3 dir = (b - a);
            float len = dir.magnitude;
            if (len <= 1e-4f) continue;
            dir /= len;

            var seg = new GameObject($"SegCol_{i}");
            seg.layer = root.layer;
            seg.transform.SetParent(root.transform, false);
            seg.transform.localPosition = mid;
            seg.transform.localRotation = Quaternion.FromToRotation(Vector3.up, dir);

            var cap = seg.AddComponent<CapsuleCollider>();
            cap.direction = 1; // Y axis
            cap.radius = segRadius;
            cap.height = Mathf.Max(len + segRadius * 2f, segRadius * 2.1f);
            cap.isTrigger = true;
        }
    }

    private void TryConfigureStaticLayer()
    {
        if (s_StaticLayerConfigured) return;
        s_StaticLayerConfigured = true;
        // Proactively ignore collisions between the static rope layer and all layers
        // Raycasts are not affected by this; interaction still works if your layer mask includes this layer
        for (int i = 0; i < 32; i++)
        {
            try { Physics.IgnoreLayerCollision(staticMeshLayer, i, true); } catch { }
        }
    }

    [ContextMenu("Create Static Mesh From Saved State")]
    public void CreateStaticMeshFromSavedState()
    {
        if (savedState == null || savedState.positions == null)
        {
            Debug.LogWarning("No saved state to create mesh from!");
            return;
        }

        // Create static mesh using pivot/local positions from saved state
        currentStaticMesh = CreateStaticMeshObjectFromPositions(savedState.positions, savedState.radius, savedState.sides);
    }

    [ContextMenu("Clear All Static Meshes")]
    public void ClearAllStaticMeshes()
    {
        foreach (GameObject obj in createdStaticMeshes)
        {
            if (obj != null)
            {
                if (Application.isPlaying) Destroy(obj); else DestroyImmediate(obj);
            }
        }
        createdStaticMeshes.Clear();
        Debug.Log("Cleared all static mesh objects");
    }

    #region Persistence

    private void SaveStateToPrefs()
    {
        if (savedState == null) return;

        string key = string.IsNullOrEmpty(customPrefsKey) ? $"RopeState|{gameObject.name}" : customPrefsKey;

        // Prefer cached endpoint IDs to avoid destruction-order issues
        string idA = cachedIdA;
        string idB = cachedIdB;
        bool isCable = cachedIsCable;

        // Fallback: try to resolve live if not cached
        if ((idA == null || idB == null) && cableComponent != null)
        {
            var a = cableComponent.StartPoint;
            var b = cableComponent.EndPoint;
            if (a != null && b != null)
            {
                var mA = a.GetComponentInParent<Machine>();
                var mB = b.GetComponentInParent<Machine>();
                var gA = a.GetComponentInParent<Generator>();
                var gB = b.GetComponentInParent<Generator>();
                var monA = a.GetComponentInParent<MachineMonitor>();
                var monB = b.GetComponentInParent<MachineMonitor>();

                idA = mA != null ? $"M:{mA.MachineId}" :
                      gA != null ? $"G:{gA.GeneratorId}" :
                      monA != null ? $"MONID:{monA.MonitorId}" : null;
                idB = mB != null ? $"M:{mB.MachineId}" :
                      gB != null ? $"G:{gB.GeneratorId}" :
                      monB != null ? $"MONID:{monB.MonitorId}" : null;

                // Treat monitors as grid participants; any two endpoints make a valid cable
                isCable = (idA != null && idB != null);
            }
        }

        var rope = new MachinePersistenceManager.RopeData
        {
            key = key,
            idA = idA,
            idB = idB,
            ropeName = savedState.ropeName,
            radius = savedState.radius,
            sides = savedState.sides,
            positions = savedState.positions,
            isCable = isCable
        };

        MachinePersistenceManager.Instance?.AddOrUpdateRope(rope);
    }

    private void CacheEndpointIds()
    {
        if (cableComponent == null) return;
        var a = cableComponent.StartPoint;
        var b = cableComponent.EndPoint;
        if (a == null || b == null) return;

        var mA = a.GetComponentInParent<Machine>();
        var mB = b.GetComponentInParent<Machine>();
        var gA = a.GetComponentInParent<Generator>();
        var gB = b.GetComponentInParent<Generator>();
        var monA = a.GetComponentInParent<MachineMonitor>();
        var monB = b.GetComponentInParent<MachineMonitor>();

        cachedIdA = mA != null ? $"M:{mA.MachineId}" :
                    gA != null ? $"G:{gA.GeneratorId}" :
                    monA != null ? $"MONID:{monA.MonitorId}" : null;
        cachedIdB = mB != null ? $"M:{mB.MachineId}" :
                    gB != null ? $"G:{gB.GeneratorId}" :
                    monB != null ? $"MONID:{monB.MonitorId}" : null;

        cachedIsCable = (cachedIdA != null && cachedIdB != null);
    }

    private void LoadSavedState()
    {
        // With MachinePersistenceManager, loading of static meshes is handled by StaticRopeLoader.
        // This method can remain a no-op for dynamic ropes.
    }

    public void ClearSavedState()
    {
        string key = string.IsNullOrEmpty(customPrefsKey) ? $"RopeState|{gameObject.name}" : customPrefsKey;
        MachinePersistenceManager.Instance?.RemoveRope(key);
        savedState = null;
        Debug.Log("Cleared saved rope state");
    }

    #endregion


    #region Editor Tools

#if UNITY_EDITOR
    [ContextMenu("Save Mesh As Asset")]
    public void SaveMeshAsAsset()
    {
        if (lastCapturedMesh == null)
        {
            Debug.LogError("No captured mesh to save! Capture the rope state first.");
            return;
        }

        string path = EditorUtility.SaveFilePanelInProject(
            "Save Rope Mesh",
            lastCapturedMesh.name,
            "asset",
            "Save the captured rope mesh as an asset"
        );

        if (!string.IsNullOrEmpty(path))
        {
            AssetDatabase.CreateAsset(lastCapturedMesh, path);
            AssetDatabase.SaveAssets();
            Debug.Log($"Saved mesh asset to: {path}");
        }
    }

    [ContextMenu("Export State As ScriptableObject")]
    public void ExportStateAsScriptableObject()
    {
        if (savedState == null)
        {
            Debug.LogError("No saved state to export!");
            return;
        }

        RopeStateAsset asset = ScriptableObject.CreateInstance<RopeStateAsset>();
        asset.stateData = savedState;

        string path = EditorUtility.SaveFilePanelInProject(
            "Save Rope State",
            $"RopeState_{savedState.ropeName}",
            "asset",
            "Save the rope state as a ScriptableObject"
        );

        if (!string.IsNullOrEmpty(path))
        {
            AssetDatabase.CreateAsset(asset, path);
            AssetDatabase.SaveAssets();
            Debug.Log($"Saved rope state asset to: {path}");
        }
    }
#endif

    #endregion
}

// ScriptableObject for persistent storage
[CreateAssetMenu(fileName = "RopeStateAsset", menuName = "Rope/Rope State Asset")]
public class RopeStateAsset : ScriptableObject
{
    public RopeStateData stateData;

    public Mesh GenerateMesh(float radius, int sides)
    {
        if (stateData == null || stateData.positions == null)
        {
            Debug.LogError("No state data in asset!");
            return null;
        }

        // This would need the mesh generation code from above
        // For brevity, returning null here
        return null;
    }
}
