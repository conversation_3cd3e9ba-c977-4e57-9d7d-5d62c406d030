{ "pid": 76288, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 76288, "tid": 1, "ts": 1757617075470894, "dur": 7864, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 76288, "tid": 1, "ts": 1757617075478763, "dur": 113711, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 76288, "tid": 1, "ts": 1757617075592488, "dur": 3040, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 76288, "tid": 67, "ts": 1757617077289668, "dur": 1068, "ph": "X", "name": "", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617075468498, "dur": 13986, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617075482486, "dur": 1793862, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617075483906, "dur": 2802, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617075486713, "dur": 2249, "ph": "X", "name": "ProcessMessages 551", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617075488966, "dur": 4672, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617075493641, "dur": 60, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617075493702, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617075493780, "dur": 541, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617075494324, "dur": 1765750, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617077260083, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617077260088, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617077260127, "dur": 1660, "ph": "X", "name": "ProcessMessages 15973", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617077261789, "dur": 4301, "ph": "X", "name": "ReadAsync 15973", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617077266096, "dur": 154, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617077266257, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617077266455, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617077266487, "dur": 304, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 76288, "tid": 12884901888, "ts": 1757617077266794, "dur": 8754, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 76288, "tid": 67, "ts": 1757617077290740, "dur": 20, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 76288, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 76288, "tid": 8589934592, "ts": 1757617075464625, "dur": 130942, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 76288, "tid": 8589934592, "ts": 1757617075595570, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 76288, "tid": 8589934592, "ts": 1757617075595574, "dur": 1430, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 76288, "tid": 67, "ts": 1757617077290761, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 76288, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 76288, "tid": 4294967296, "ts": 1757617075444039, "dur": 1833523, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 76288, "tid": 4294967296, "ts": 1757617075449496, "dur": 9774, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 76288, "tid": 4294967296, "ts": 1757617077277620, "dur": 5066, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 76288, "tid": 4294967296, "ts": 1757617077280864, "dur": 30, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 76288, "tid": 4294967296, "ts": 1757617077282784, "dur": 15, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 76288, "tid": 67, "ts": 1757617077290766, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1757617075480091, "dur":3743, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617075483844, "dur":177, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617075484055, "dur":635, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617075484747, "dur":94, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617075484842, "dur":1781029, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617077265872, "dur":95, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617077265967, "dur":78, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617077266193, "dur":3191, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1757617075485523, "dur":1259, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1757617075484919, "dur":7459, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1757617075494266, "dur":1766475, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1757617075484965, "dur":111790, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617075607314, "dur":541, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1757617075596756, "dur":11104, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617075607860, "dur":1658013, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617075485005, "dur":1780845, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617075485000, "dur":122864, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617075607865, "dur":1657987, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617075485032, "dur":1780817, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617075485067, "dur":1780786, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617075485099, "dur":1780747, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617075485136, "dur":1780712, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617075485166, "dur":1780704, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617075485196, "dur":1780674, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617075485244, "dur":1780632, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617075485286, "dur":1780575, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617077274058, "dur":672, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 76288, "tid": 67, "ts": 1757617077291094, "dur": 1497, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 76288, "tid": 67, "ts": 1757617077292752, "dur": 2212, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 76288, "tid": 67, "ts": 1757617077288824, "dur": 7120, "ph": "X", "name": "Write chrome-trace events", "args": {} },
