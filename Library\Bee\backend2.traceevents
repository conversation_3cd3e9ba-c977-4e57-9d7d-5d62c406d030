{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1757617110248666, "dur":55, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617110248758, "dur":88121, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617110336885, "dur":399, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617110337374, "dur":354, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617110338007, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":0, "ts":1757617110342281, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst.Unsafe-FeaturesChecked.txt_1w2c.info" }}
,{ "pid":12345, "tid":0, "ts":1757617110342350, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1757617110342611, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Mathematics-FeaturesChecked.txt_rnku.info" }}
,{ "pid":12345, "tid":0, "ts":1757617110344402, "dur":122, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1757617110344722, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterInputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1757617110345527, "dur":82, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.HotReloadModule-FeaturesChecked.txt_p7t3.info" }}
,{ "pid":12345, "tid":0, "ts":1757617110348993, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":0, "ts":1757617110337749, "dur":11425, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617110349181, "dur":86354, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617110435536, "dur":165, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617110435838, "dur":3347, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1757617110338052, "dur":11155, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110354575, "dur":795, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"../../Editors/6000.2.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1757617110355371, "dur":507, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":1, "ts":1757617110355879, "dur":1482, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.1f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1757617110357361, "dur":16900, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.1f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1757617110374262, "dur":295, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":1, "ts":1757617110374558, "dur":3798, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":1, "ts":1757617110378356, "dur":268, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":1, "ts":1757617110378625, "dur":560, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1757617110379185, "dur":527, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":1, "ts":1757617110379712, "dur":79, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":1, "ts":1757617110379792, "dur":113, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1757617110349215, "dur":30706, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110379936, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":1, "ts":1757617110380008, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110380167, "dur":1511, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1757617110381679, "dur":17772, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110407919, "dur":13557, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1757617110421477, "dur":442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110423436, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1757617110423580, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110423740, "dur":672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110425631, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110425770, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110425948, "dur":281, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110426251, "dur":379, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110426639, "dur":273, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110426950, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110429444, "dur":508, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110431243, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110431402, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110432889, "dur":637, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1757617110434723, "dur":784, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110338069, "dur":11195, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110349732, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110350400, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110351072, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110351666, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110352248, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110352851, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110353445, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110354057, "dur":1025, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110355082, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110355694, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110356320, "dur":922, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":2, "ts":1757617110357243, "dur":646, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110357889, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110358465, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110359113, "dur":537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110359650, "dur":570, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110360221, "dur":1390, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110361612, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110362312, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110349272, "dur":13640, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110366184, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Security.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110366937, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Security.Win32.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110367476, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Profiler.Log.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110362912, "dur":8148, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110372157, "dur":980, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110373137, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.PropertiesModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110373831, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110376106, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.MarshallingModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110371061, "dur":6759, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110377820, "dur":2108, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110379953, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110380665, "dur":87, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Net.Http-FeaturesChecked.txt_iku7.info" }}
,{ "pid":12345, "tid":2, "ts":1757617110380759, "dur":297, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110381064, "dur":651, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110381742, "dur":707, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110382455, "dur":650, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110383113, "dur":661, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110383783, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110384414, "dur":1334, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110385754, "dur":620, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110386383, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110386944, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110387553, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110388234, "dur":719, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110388958, "dur":358, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/UnityPlayer.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110389317, "dur":495, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110389817, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110389961, "dur":374, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110390345, "dur":969, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110391322, "dur":16599, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110407928, "dur":616, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110408545, "dur":1019, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110409572, "dur":349, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110409922, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110410118, "dur":570, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ContentLoadModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1757617110410692, "dur":459, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110412262, "dur":433, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1757617110412695, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110413863, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110414005, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1757617110414149, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110414276, "dur":324, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110414624, "dur":756, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Antlr3.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1757617110415381, "dur":404, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110416936, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110418701, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110418875, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110420660, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VideoModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1757617110420833, "dur":1404, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110423519, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110425545, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110426815, "dur":857, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110429495, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110429717, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110429884, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110430047, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1757617110430110, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110431906, "dur":411, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110432325, "dur":300, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110432664, "dur":872, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1757617110434826, "dur":680, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110338056, "dur":11159, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110349797, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\WindowsBase.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110350466, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1757617110351089, "dur":565, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":3, "ts":1757617110351655, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":3, "ts":1757617110352226, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110352839, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":3, "ts":1757617110353433, "dur":618, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110354051, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110354598, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":3, "ts":1757617110355261, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110355779, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110356437, "dur":888, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110357325, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110357962, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110358523, "dur":635, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110359159, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110359704, "dur":1427, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110349227, "dur":11904, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110361132, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":3, "ts":1757617110361744, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1757617110361132, "dur":4703, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110366407, "dur":1064, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110372564, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110365835, "dur":7327, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110373162, "dur":157, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110373319, "dur":6608, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110379950, "dur":852, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110380809, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110381384, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110381515, "dur":540, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110382076, "dur":774, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110382860, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110383531, "dur":605, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110384141, "dur":950, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110385097, "dur":1154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110386256, "dur":723, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110386985, "dur":619, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110387610, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110388309, "dur":700, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110389015, "dur":389, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":3, "ts":1757617110389404, "dur":404, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110389813, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110389941, "dur":363, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110390305, "dur":91, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110390399, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110391019, "dur":16887, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110407908, "dur":622, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/BakeryRuntimeAssembly-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1757617110408531, "dur":445, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110409970, "dur":640, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.SpriteMaskModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110410611, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110410787, "dur":228, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.UnityCurlModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110411016, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110411215, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.WindModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1757617110411424, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110412669, "dur":363, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110413033, "dur":202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110413239, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1757617110413409, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110414777, "dur":370, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110416406, "dur":288, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":3, "ts":1757617110416707, "dur":261, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110416975, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.DirectorModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1757617110417042, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110418904, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110420764, "dur":1502, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110423609, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110423754, "dur":744, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110425740, "dur":208, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110425956, "dur":266, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110426229, "dur":316, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110426583, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110428757, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110428872, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110429014, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110429132, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110429236, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110429358, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110429582, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110429759, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110429949, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110431769, "dur":553, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110433906, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1757617110435393, "dur":146, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110338096, "dur":11175, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110349279, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110349789, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110350419, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110351047, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110351633, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110352223, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110352799, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110353426, "dur":619, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110354045, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110354611, "dur":609, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110355700, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110356379, "dur":903, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110357282, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110357919, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110358516, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110359126, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110359666, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110360247, "dur":733, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110360980, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110349278, "dur":12313, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110361591, "dur":692, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Analytics.deps.json" }}
,{ "pid":12345, "tid":4, "ts":1757617110361591, "dur":3778, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110366109, "dur":565, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110366675, "dur":997, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110370619, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Cryptography.RSA.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110365369, "dur":7259, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110372629, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110373420, "dur":521, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110373941, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110372629, "dur":4566, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110377196, "dur":2818, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110380017, "dur":718, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110380735, "dur":216, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_vic3.info" }}
,{ "pid":12345, "tid":4, "ts":1757617110380954, "dur":448, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110381414, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110381975, "dur":598, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110382579, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110383192, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110383881, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110384510, "dur":928, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110385447, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110386121, "dur":605, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110386732, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110387390, "dur":654, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110388050, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110388631, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110389182, "dur":187, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":4, "ts":1757617110389370, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110389570, "dur":506, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":4, "ts":1757617110390076, "dur":225, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110390355, "dur":780, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110391139, "dur":16826, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110407966, "dur":595, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.Collections.LowLevel.ILSupport.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110408562, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110409299, "dur":305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.NVIDIAModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1757617110409605, "dur":394, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110410570, "dur":397, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110411932, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110412048, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110412173, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110412306, "dur":473, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GameCenterModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1757617110412780, "dur":355, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110414083, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.TerrainPhysicsModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1757617110414220, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110414350, "dur":187, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.ShaderLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1757617110414538, "dur":287, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110415948, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110416118, "dur":344, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110417576, "dur":605, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110419362, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110421364, "dur":973, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110423693, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1757617110423746, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110425708, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110425952, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110427200, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110427794, "dur":1224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110430052, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110430212, "dur":655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110431955, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110432194, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110432328, "dur":247, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110432580, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1757617110432632, "dur":644, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110434416, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110434542, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110434718, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1757617110434846, "dur":688, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110338124, "dur":11156, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110349720, "dur":619, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110350340, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110350938, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110351530, "dur":553, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110352083, "dur":607, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110352691, "dur":575, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110353267, "dur":621, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110353888, "dur":833, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110354721, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110355323, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110355941, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110356612, "dur":822, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110357435, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110358048, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110358614, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110359200, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110359730, "dur":773, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110349289, "dur":12179, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110361469, "dur":790, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.TvOS.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110362259, "dur":501, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.MacOS.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110361469, "dur":4276, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110366391, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Cryptography.Encryption.ECDsa.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110366989, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Security.Cryptography.Encryption.ECDiffieHellman.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110365745, "dur":6523, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110372268, "dur":1153, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.InputForUIModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110373422, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.ImageConversionModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110374571, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110376078, "dur":649, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.GraphicsStateCollectionSerializerModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110372268, "dur":6114, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110378383, "dur":1610, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110379999, "dur":801, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110380805, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110381300, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110381803, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110382357, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110383024, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110383687, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110384314, "dur":815, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110385135, "dur":819, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110385963, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110386625, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110387247, "dur":680, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110387936, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110388513, "dur":624, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110389141, "dur":293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":5, "ts":1757617110389435, "dur":242, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110389682, "dur":347, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":5, "ts":1757617110390030, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110390355, "dur":812, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110391170, "dur":16755, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110407926, "dur":676, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.Collections.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110408602, "dur":873, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110409482, "dur":342, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110409825, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110410075, "dur":619, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1757617110410695, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110412286, "dur":464, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1757617110412754, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110414043, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110414188, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.UnityWebRequestTextureModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1757617110414356, "dur":278, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110414641, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110414806, "dur":783, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.LocalizationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1757617110415590, "dur":380, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110417018, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110417166, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110417342, "dur":526, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110418912, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110420676, "dur":756, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110423011, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110424945, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110426439, "dur":330, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110426805, "dur":772, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110429442, "dur":240, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110429721, "dur":448, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110431513, "dur":213, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110431767, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110433492, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1757617110435155, "dur":364, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110338155, "dur":11220, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110349382, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110350045, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110350696, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110351318, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110351884, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110352475, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110353087, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110353685, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110354442, "dur":935, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110355377, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110356061, "dur":925, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110356987, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110357659, "dur":570, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110358229, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110358828, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110359356, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110359927, "dur":1073, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110361470, "dur":767, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110349382, "dur":13310, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110363139, "dur":531, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110366065, "dur":530, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110366596, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.Formatters.Soap.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110362692, "dur":7896, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110374149, "dur":503, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110370588, "dur":4403, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110374991, "dur":5010, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110380007, "dur":772, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110380786, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110381412, "dur":640, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110382058, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110382780, "dur":739, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110383527, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110384137, "dur":1087, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110385229, "dur":679, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110385916, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110386644, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110387236, "dur":624, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110387867, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110388468, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110389095, "dur":269, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":6, "ts":1757617110389364, "dur":322, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110389690, "dur":349, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":6, "ts":1757617110390040, "dur":360, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110390406, "dur":574, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110390986, "dur":16924, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110407912, "dur":614, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1757617110408527, "dur":344, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110410071, "dur":595, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MultiplayerModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1757617110410667, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110412314, "dur":530, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1757617110412845, "dur":419, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110414287, "dur":330, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110414627, "dur":577, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110415205, "dur":615, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110415827, "dur":224, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110416051, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110416270, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1757617110416420, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110416595, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110416762, "dur":324, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Flow-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1757617110417089, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110418828, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110420652, "dur":1300, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110423310, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110425223, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110426636, "dur":295, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110426979, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110429429, "dur":245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110429714, "dur":523, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110431533, "dur":208, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110431748, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110431898, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110432189, "dur":334, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110434037, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1757617110435441, "dur":71, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110338174, "dur":11231, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110349413, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110349986, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110350630, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110351248, "dur":518, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110351766, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110352338, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110352936, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110353522, "dur":606, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110354129, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110354760, "dur":820, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110355580, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110356158, "dur":953, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110357111, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110357788, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110358366, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110358979, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110359513, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110360029, "dur":806, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110361161, "dur":766, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110349413, "dur":12514, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110361927, "dur":2723, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110366356, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110364650, "dur":5516, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110372318, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110373060, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Data.SqlClient.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110370167, "dur":5568, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110375735, "dur":4194, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110379953, "dur":777, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110380731, "dur":211, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Memory-FeaturesChecked.txt_mhrh.info" }}
,{ "pid":12345, "tid":7, "ts":1757617110380945, "dur":467, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110381419, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110382024, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110382583, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110383205, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110383878, "dur":884, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110384772, "dur":876, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110385656, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110386207, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110386772, "dur":667, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110387445, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110388122, "dur":775, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110388916, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110389057, "dur":244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":7, "ts":1757617110389302, "dur":289, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110389595, "dur":493, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":7, "ts":1757617110390089, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110390326, "dur":476, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110390809, "dur":296, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110391109, "dur":16806, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110407916, "dur":648, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AMDModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1757617110408565, "dur":503, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110410027, "dur":669, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1757617110410696, "dur":518, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110412355, "dur":499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1757617110412855, "dur":406, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110414317, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110414631, "dur":697, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1757617110415329, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110415469, "dur":501, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterInputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1757617110415971, "dur":486, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110417540, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110419350, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110421429, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110421567, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110421689, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110421799, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110421990, "dur":692, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110423880, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110425888, "dur":425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110427057, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110427725, "dur":1221, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110430064, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110431849, "dur":442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110433611, "dur":751, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1757617110435325, "dur":205, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110338196, "dur":11251, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110349453, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110350053, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110350748, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110351373, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110351950, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110352517, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110353098, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110353754, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110354365, "dur":636, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110355453, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110356052, "dur":824, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110356876, "dur":740, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110357616, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110358214, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110358792, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110359347, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110359887, "dur":1821, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110361708, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110362415, "dur":503, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110349453, "dur":13465, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110366235, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110366877, "dur":631, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Utilities.v4.0.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110367508, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Tasks.v4.0.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110368472, "dur":553, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Engine.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110362919, "dur":8125, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110372112, "dur":1100, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110373583, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110374412, "dur":532, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110376113, "dur":771, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110371045, "dur":6438, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110377484, "dur":2468, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110379957, "dur":767, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110380724, "dur":135, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.IO.Compression.FileSystem-FeaturesChecked.txt_caef.info" }}
,{ "pid":12345, "tid":8, "ts":1757617110380867, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110381614, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110382224, "dur":685, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110382915, "dur":694, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110383615, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110384270, "dur":1023, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110385300, "dur":845, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110386151, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110386762, "dur":640, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110387407, "dur":672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110388084, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110388719, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110389188, "dur":197, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":8, "ts":1757617110389385, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110389589, "dur":515, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":8, "ts":1757617110390105, "dur":204, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110390309, "dur":134, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":8, "ts":1757617110390445, "dur":1017, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110391468, "dur":16443, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110407919, "dur":623, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1757617110408543, "dur":735, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110410190, "dur":560, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1757617110410751, "dur":446, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110412305, "dur":481, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1757617110412786, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110414275, "dur":267, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110414551, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110414788, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110416384, "dur":223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.DSPGraphModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1757617110416607, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110416788, "dur":482, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110418346, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110420101, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110420230, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110420359, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110420537, "dur":876, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110423202, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110425176, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110425306, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110425433, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110425626, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110426833, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterRendererModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1757617110426887, "dur":856, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110429563, "dur":522, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110431468, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110431661, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110431857, "dur":258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110432126, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110432257, "dur":282, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110432574, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110434529, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1757617110434727, "dur":801, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110338212, "dur":11295, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110349514, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110350140, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110350798, "dur":606, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110351405, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110351998, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110352583, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110353178, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110353812, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110354554, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110355651, "dur":568, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110356219, "dur":947, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110357166, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110357826, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110358423, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110359110, "dur":570, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110359680, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110360251, "dur":1412, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110361664, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110349514, "dur":13250, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110363054, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110366213, "dur":950, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110367163, "dur":681, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.WebRequest.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110369750, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IdentityModel.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110362764, "dur":8190, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110372435, "dur":966, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110373401, "dur":668, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110374536, "dur":667, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110376069, "dur":835, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110370955, "dur":6690, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110377645, "dur":2343, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110379993, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110380736, "dur":228, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.IO.Compression-FeaturesChecked.txt_f62v.info" }}
,{ "pid":12345, "tid":9, "ts":1757617110380972, "dur":468, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110381453, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110382153, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110382881, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110383600, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110384223, "dur":768, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110384998, "dur":954, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110385959, "dur":601, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110386566, "dur":506, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110387081, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110387695, "dur":618, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110388318, "dur":700, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110389023, "dur":325, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":9, "ts":1757617110389348, "dur":315, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110389671, "dur":378, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":9, "ts":1757617110390050, "dur":342, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110390398, "dur":752, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110391155, "dur":16797, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110407953, "dur":591, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110408545, "dur":831, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110409384, "dur":393, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.MarshallingModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110409778, "dur":208, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110409993, "dur":586, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110410580, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110410771, "dur":352, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.IdentifiersModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1757617110411128, "dur":466, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110412528, "dur":246, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110413558, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":9, "ts":1757617110413694, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110413824, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110414015, "dur":462, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110415173, "dur":698, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityConsentModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1757617110415872, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110417429, "dur":578, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110419105, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110420749, "dur":1305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110423361, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110423485, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110423651, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110425592, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110425778, "dur":466, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110426949, "dur":279, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110427238, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110427426, "dur":358, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110427792, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110427927, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110428048, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110428168, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110428287, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110428408, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110428589, "dur":710, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110430273, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110430375, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110430537, "dur":651, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110432210, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110432503, "dur":560, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1757617110434410, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110338236, "dur":11279, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110349522, "dur":580, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110350102, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110350781, "dur":621, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110351402, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110351995, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110352576, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110353162, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110353806, "dur":963, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110354770, "dur":537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110355307, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110355857, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110356547, "dur":934, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110357481, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110358075, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110358671, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110359259, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110359772, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110360514, "dur":614, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110361128, "dur":641, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110361770, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110349522, "dur":12835, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110366269, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Http.SelfHost.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110362357, "dur":6936, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110370703, "dur":640, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110372712, "dur":975, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110369294, "dur":6238, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110375533, "dur":4390, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110379983, "dur":870, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110380861, "dur":697, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":10, "ts":1757617110381559, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110381711, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110382264, "dur":698, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110382970, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110383677, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110384305, "dur":737, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110385048, "dur":896, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110385951, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110386539, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110387094, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110387740, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110388368, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110389079, "dur":233, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":10, "ts":1757617110389313, "dur":281, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110389599, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":10, "ts":1757617110389734, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110389864, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110390008, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110390181, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110390304, "dur":102, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":10, "ts":1757617110390409, "dur":782, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110391195, "dur":16761, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110407962, "dur":598, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110408561, "dur":736, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110409302, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.ClusterRendererModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110409565, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110409712, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1757617110409951, "dur":349, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110411059, "dur":190, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1757617110411249, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110412592, "dur":377, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110412973, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110413129, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.ServiceModel.Internals-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1757617110413324, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110414584, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110414822, "dur":335, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110415634, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110415821, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110416060, "dur":204, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110416280, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110416411, "dur":294, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1757617110416706, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110416850, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InsightsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1757617110417007, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110418749, "dur":573, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110420536, "dur":748, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110422912, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110424849, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110426390, "dur":320, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110426720, "dur":231, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110426958, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110427545, "dur":1187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110429982, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110431852, "dur":384, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110433359, "dur":598, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1757617110435080, "dur":451, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110338257, "dur":11270, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110349533, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110350126, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110350784, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110351396, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110351965, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110352553, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110353155, "dur":623, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110353778, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110354430, "dur":783, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110355702, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110356387, "dur":902, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110357289, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110357910, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110358496, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110359116, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110359665, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110360244, "dur":839, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110361083, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110361685, "dur":664, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110349533, "dur":12817, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110366141, "dur":525, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110366666, "dur":667, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110362350, "dur":7329, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110369679, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110370994, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110372590, "dur":607, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110373197, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110369679, "dur":5988, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110375667, "dur":4265, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110379978, "dur":804, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110380790, "dur":507, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110381307, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110381875, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110382443, "dur":667, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110383129, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110383808, "dur":593, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110384410, "dur":886, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110385302, "dur":876, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110386188, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110386818, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110387468, "dur":680, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110388154, "dur":764, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110388925, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110389133, "dur":311, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":11, "ts":1757617110389445, "dur":303, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110389754, "dur":211, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":11, "ts":1757617110389966, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110390143, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":11, "ts":1757617110390296, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110390441, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110391113, "dur":16804, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110407918, "dur":638, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Data.DataSetExtensions-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1757617110408557, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110409957, "dur":490, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression.FileSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1757617110410447, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110411475, "dur":166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1757617110411642, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110412725, "dur":329, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.AccessibilityModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110413054, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110413298, "dur":251, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1757617110413553, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110414804, "dur":890, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.TextMeshPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1757617110415695, "dur":484, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110416967, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1757617110417131, "dur":543, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110418850, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1757617110418979, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110419136, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110420743, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110420870, "dur":925, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110421828, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110422001, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110424053, "dur":741, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110425929, "dur":271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110426207, "dur":317, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110426531, "dur":300, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110426838, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110426978, "dur":388, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110427393, "dur":342, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110427775, "dur":1209, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110430034, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1757617110430103, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110431886, "dur":287, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110432211, "dur":379, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110433841, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1757617110435360, "dur":144, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110338277, "dur":11300, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110349578, "dur":670, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110350248, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110350883, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110351492, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110352077, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110352676, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_8.0.424.16909.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110353257, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110353861, "dur":869, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110354731, "dur":833, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110355564, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110356206, "dur":940, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110357146, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110357800, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110358388, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110359017, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110359584, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110360181, "dur":1044, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110361225, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110361855, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110349578, "dur":13319, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110366176, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Services.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110367382, "dur":635, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.OracleClient.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110362897, "dur":7754, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110372787, "dur":1182, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110370652, "dur":4652, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110375305, "dur":4620, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110379954, "dur":830, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110380796, "dur":471, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110381277, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110381995, "dur":677, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110382679, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110383371, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110384025, "dur":885, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110384927, "dur":1040, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110385983, "dur":705, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110386697, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110387332, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110388010, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110388628, "dur":542, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110389174, "dur":261, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":12, "ts":1757617110389435, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110389657, "dur":293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":12, "ts":1757617110389951, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110390120, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object.exe" }}
,{ "pid":12345, "tid":12, "ts":1757617110390256, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110390386, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110390510, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110391044, "dur":16864, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110407909, "dur":637, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TLSModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1757617110408546, "dur":459, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110410031, "dur":289, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110411076, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110411245, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1757617110411450, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110412692, "dur":367, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/PersistentObject/Persistent Object_Data/Managed/UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1757617110413059, "dur":211, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110413278, "dur":270, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1757617110413549, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110414834, "dur":966, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityConnectModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1757617110415801, "dur":593, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110417608, "dur":634, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110419455, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110421954, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110422115, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.HotReloadModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1757617110422195, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110424042, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110425923, "dur":266, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110426198, "dur":312, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110426520, "dur":289, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110426847, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110429355, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110429548, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110431429, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110431567, "dur":235, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110431838, "dur":415, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110433412, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110433571, "dur":694, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1757617110435271, "dur":234, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1757617110444194, "dur":3189, "ph":"X", "name": "ProfilerWriteOutput" }
,