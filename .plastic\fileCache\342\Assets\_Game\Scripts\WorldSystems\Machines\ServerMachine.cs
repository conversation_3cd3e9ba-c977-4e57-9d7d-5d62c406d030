using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Server machine that mines currency when powered and running
/// </summary>
public class ServerMachine : Machine
{
    [Header("Mining Settings")]
    [SerializeField] private int baseCurrencyPerOperation = 5;
    [SerializeField] private float miningEfficiency = 1.0f;
    [SerializeField] private float efficiencyDecayRate = 0.001f; // Per operation

    [Header("Visual Effects")]
    [SerializeField] private ParticleSystem miningEffect;
    [SerializeField] private AudioSource miningSound;
    [SerializeField] private Light statusLight;

    private int storedCurrency = 0;
    private float currentMiningEfficiency;

    public int StoredCurrency => storedCurrency;
    public float CurrentMiningEfficiency => currentMiningEfficiency;

    protected override void Awake()
    {
        base.Awake();
        machineType = MachineType.Server;
        currentMiningEfficiency = miningEfficiency;
    }

    protected override void PerformOperation()
    {
        if (!isPowered && requiresPower) return;

        // Calculate mining amount based on efficiency
        int minedAmount = Mathf.RoundToInt(baseCurrencyPerOperation * currentMiningEfficiency);
        storedCurrency += minedAmount;

        // Decay efficiency slightly over time
        currentMiningEfficiency = Mathf.Max(0.1f, currentMiningEfficiency - efficiencyDecayRate);

        // Visual/audio feedback
        PlayMiningEffects();

        // Optional: Notify player of mining
        //Debug.Log($"{MachineName} mined {minedAmount} currency. Total stored: {storedCurrency}");
    }

    protected override void OnMachineStarted()
    {
        //Debug.Log($"{MachineName} server started mining");
        UpdateVisualState(true);
    }

    protected override void OnMachineStopped()
    {
        //Debug.Log($"{MachineName} server stopped mining");
        UpdateVisualState(false);
    }

    protected override void OnPowerStateChanged()
    {
        base.OnPowerStateChanged();
        UpdateVisualState(CurrentState == MachineState.Running);
    }

    private void UpdateVisualState(bool isRunning)
    {
        // Update particle effects
        if (miningEffect != null)
        {
            if (isRunning && !miningEffect.isPlaying)
            {
                miningEffect.Play();
            }
            else if (!isRunning && miningEffect.isPlaying)
            {
                miningEffect.Stop();
            }
        }

        // Update audio
        if (miningSound != null)
        {
            if (isRunning && !miningSound.isPlaying)
            {
                miningSound.Play();
            }
            else if (!isRunning && miningSound.isPlaying)
            {
                miningSound.Stop();
            }
        }

        // Update light
        if (statusLight != null)
        {
            statusLight.enabled = isPowered;
            if (isPowered)
            {
                statusLight.color = isRunning ? Color.green : Color.yellow;
                statusLight.intensity = isRunning ? 2f : 1f;
            }
        }
    }

    private void PlayMiningEffects()
    {
        // Additional mining-specific effects could go here
        // Like screen shake, additional particles, etc.
    }

    public int CollectCurrency()
    {
        if (storedCurrency <= 0) return 0;

        // Add to player's currency system
        var currencyManager = FindFirstObjectByType<CurrencyManager>();
        if (currencyManager != null)
        {
            currencyManager.AddCurrency(storedCurrency);
            int collected = storedCurrency;
            storedCurrency = 0;
            return collected;
        }
        return 0;
    }

    public void Maintenance()
    {
        // Reset efficiency during maintenance
        currentMiningEfficiency = miningEfficiency;
        Debug.Log($"{MachineName} maintenance completed - efficiency restored");
    }

    public override string GetMachineStatus()
    {
        string status = base.GetMachineStatus();
        status += $"Currency Stored: {storedCurrency}\n";
        status += $"Mining Efficiency: {(currentMiningEfficiency * 100f):F1}%\n";
        status += $"Avg Output: {(baseCurrencyPerOperation * currentMiningEfficiency):F1}/op\n";
        return status;
    }

    public override Dictionary<string, object> GetMachineData()
    {
        var data = base.GetMachineData();
        data["storedCurrency"] = storedCurrency;
        data["currentMiningEfficiency"] = currentMiningEfficiency;
        data["baseCurrencyPerOperation"] = baseCurrencyPerOperation;
        return data;
    }

    // Apply persisted state on load
    public void ApplyPersistedState(int persistedStoredCurrency, float persistedEfficiency)
    {
        storedCurrency = Mathf.Max(0, persistedStoredCurrency);
        if (persistedEfficiency > 0f)
        {
            currentMiningEfficiency = persistedEfficiency;
        }
    }
}
