{ "pid": 77856, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 77856, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 77856, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 77856, "tid": 1, "ts": 1757617102702540, "dur": 1370572, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 77856, "tid": 1, "ts": 1757617102704421, "dur": 395104, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 77856, "tid": 1, "ts": 1757617103048425, "dur": 50578, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 77856, "tid": 1, "ts": 1757617103159746, "dur": 16782, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 77856, "tid": 1, "ts": 1757617103178140, "dur": 159061, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 77856, "tid": 1, "ts": 1757617103337243, "dur": 477692, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 77856, "tid": 1, "ts": 1757617103814955, "dur": 246549, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 77856, "tid": 1, "ts": 1757617104069548, "dur": 3386, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 77856, "tid": 1, "ts": 1757617104073114, "dur": 466, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 77856, "tid": 1, "ts": 1757617104082384, "dur": 2075, "ph": "X", "name": "", "args": {} },
{ "pid": 77856, "tid": 1, "ts": 1757617104081573, "dur": 3227, "ph": "X", "name": "Write chrome-trace events", "args": {} },
