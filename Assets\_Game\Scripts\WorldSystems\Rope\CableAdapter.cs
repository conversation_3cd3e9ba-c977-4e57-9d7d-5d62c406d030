using UnityEngine;

/// <summary>
/// Small helper to centralize CableComponent configuration via its public API.
/// Kept for backwards compatibility with existing call sites.
/// </summary>
public static class CableAdapter
{
    public static void TrySetCableLength(CableComponent cable, float length)
    {
        if (cable == null) return;
        try { cable.CableLength = length; } catch { /* ignore */ }
    }

    public static void TrySetTotalSegments(CableComponent cable, int segments)
    {
        if (cable == null) return;
        try { cable.TotalSegments = Mathf.Clamp(segments, 2, 256); } catch { /* ignore */ }
    }

    public static void TrySetRadius(CableComponent cable, float radius)
    {
        if (cable == null) return;
        try { cable.Radius = Mathf.Max(0.0001f, radius); } catch { /* ignore */ }
    }

    public static float GetRadius(CableComponent cable, float fallback = 0.05f)
    {
        if (cable == null) return fallback;
        try { return cable.Radius; } catch { return fallback; }
    }

    public static void TrySetMass(CableComponent cable, float mass)
    {
        if (cable == null) return;
        try { cable.CableMass = Mathf.Max(0f, mass); } catch { /* ignore */ }
    }

    public static void TrySetStiffness(CableComponent cable, float stiffness)
    {
        if (cable == null) return;
        try { cable.Stiffness = Mathf.Clamp01(stiffness); } catch { /* ignore */ }
    }

    /// <summary>
    /// Best-effort segment count retrieval.
    /// </summary>
    public static int GetSegmentCount(CableComponent cable)
    {
        if (cable == null) return 24;
        try { return Mathf.Clamp(cable.GetSegmentCount(), 2, 256); } catch { return 24; }
    }
}
