using UnityEngine;
using System.Collections.Generic;

public class StaticRopeLoader : MonoBehaviour
{
    [Header("Rope State Assets")]
    [SerializeField] private List<RopeStateAsset> ropeAssets = new List<RopeStateAsset>();
    
    [Header("Mesh Generation Settings")]
    [SerializeField] private float defaultRadius = 0.05f;
    [SerializeField] private int defaultSides = 8;
    [SerializeField] private Material ropeMaterial;
    [SerializeField] private bool addColliders = false;
    [SerializeField] private bool optimizeMeshes = true;
    [SerializeField] private bool enableInteraction = true;
    [SerializeField] private string interactRopeItemName = "Rope";
    [SerializeField] private int interactRopeItemQuantity = 1;
    [SerializeField] private bool consumeOnInteract = true;

    [Header("Static Layer")]
    [Tooltip("Layer index used for loaded static rope meshes (set to a non-colliding layer).")]
    [SerializeField] private int staticMeshLayer = 8;
    
    [Header("Auto-Load Settings")]
    [SerializeField] private bool loadOnStart = true;
    [SerializeField] private bool loadFromPersistence = true;
    [SerializeField] private bool purgeSceneOrphansOnStart = true;
    
    [Header("Debug")] 
    [SerializeField] private bool debugLogs = false;
    
    [Header("Post-Load Grid Recompute")]
    [SerializeField] private bool enforceGeneratorStartOnLoad = true;
    [SerializeField] private float postLoadGridDelay = 0.5f;
    
    private List<GameObject> loadedRopes = new List<GameObject>();
    private static bool s_StaticLayerConfigured = false;
    
    void Start()
    {
        if (loadOnStart)
        {
            StartCoroutine(LoadAfterDelay());
        }
    }

    private System.Collections.IEnumerator LoadAfterDelay()
    {
        // Wait a frame to allow MachinePersistenceManager to load
        yield return null;
        // Ensure persistence manager exists in scenes where it's not present
        MachinePersistenceManager.Ensure();
        TryConfigureStaticLayer();
        LoadAllRopes();
    }
    
    [ContextMenu("Load All Ropes")]
    public void LoadAllRopes()
    {
        // Clear existing
        ClearLoadedRopes();

        // Load from assets (skip any that player has removed previously)
        foreach (var asset in ropeAssets)
        {
            if (asset != null && asset.stateData != null)
            {
                string assetKey = MakeAssetRopeKey(asset);
                if (loadFromPersistence && MachinePersistenceManager.Instance != null && MachinePersistenceManager.Instance.IsAssetRopeRemoved(assetKey))
                {
                    if (debugLogs) Debug.Log($"[StaticRopeLoader] Skipping removed asset rope: {assetKey}");
                    continue;
                }
                var staticObj = CreateStaticRope(asset.stateData);
                // Configure interactable so pickup can mark this asset rope as removed across sessions
                if (enableInteraction && staticObj != null)
                {
                    var interact = staticObj.GetComponent<StaticRopeInteractable>();
                    if (interact != null)
                    {
                        interact.Configure(assetKey, false, null, null);
                    }
                }
            }
        }

        // Load from MachinePersistenceManager
        if (loadFromPersistence)
        {
            if (debugLogs)
            {
                int savedCount = MachinePersistenceManager.Instance != null ? MachinePersistenceManager.Instance.GetRopes().Count : -1;
                Debug.Log($"[StaticRopeLoader] Loading from persistence. Saved ropes: {savedCount}");
            }
            LoadFromPersistenceManager();
        }

        if (purgeSceneOrphansOnStart)
        {
            PurgeSceneOrphans();
        }

        if (debugLogs) Debug.Log($"[StaticRopeLoader] Loaded {loadedRopes.Count} static ropes");

        // After all ropes are processed, ensure the power grid is coherent
        if (enforceGeneratorStartOnLoad)
        {
            StartCoroutine(PostLoadGridRecompute());
        }
    }

    private void LoadFromPersistenceManager()
    {
        var mgr = MachinePersistenceManager.Instance;
        if (mgr == null) return;
        var ropes = mgr.GetRopes();
        string activeScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        HashSet<string> loadedPairs = new HashSet<string>();
        HashSet<string> loadedKeys = new HashSet<string>();

        foreach (var rope in ropes)
        {
            // Only load ropes for the current scene
            if (!string.IsNullOrEmpty(rope.sceneName) && rope.sceneName != activeScene)
                continue;

            if (rope == null || rope.positions == null || rope.positions.Length < 2) continue;

            bool endpointsOk = true;
            if (rope.isCable && !string.IsNullOrEmpty(rope.idA) && !string.IsNullOrEmpty(rope.idB))
            {
                if (!EndpointsExist(rope.idA, rope.idB))
                {
                    endpointsOk = false;
                }
                else
                {
                    // De-duplicate by endpoint pair for ALL cables: skip extras, never delete
                    string pair = CanonicalPair(rope.idA, rope.idB);
                    if (loadedPairs.Contains(pair))
                    {
                        if (debugLogs) Debug.Log($"[StaticRopeLoader] Skipping duplicate pair {pair} for key {rope.key}");
                        continue;
                    }
                    loadedPairs.Add(pair);
                }
            }

            // De-dupe by key
            if (!string.IsNullOrEmpty(rope.key))
            {
                if (loadedKeys.Contains(rope.key))
                {
                    if (debugLogs) Debug.Log($"[StaticRopeLoader] Skipping duplicate rope key {rope.key}");
                    continue;
                }
                loadedKeys.Add(rope.key);
            }

            var data = new RopeStateData(rope.positions, rope.radius, rope.sides, rope.ropeName);
            GameObject staticObj = CreateStaticRope(data);
            if (debugLogs)
            {
                Debug.Log($"[StaticRopeLoader] Created static mesh for key={rope.key}, isCable={rope.isCable}, idA={rope.idA}, idB={rope.idB}, points={rope.positions?.Length ?? 0}");
            }

            // Configure interactable with persistence and endpoint info
            if (enableInteraction && staticObj != null)
            {
                var interact = staticObj.GetComponent<StaticRopeInteractable>();
                if (interact != null)
                {
                    interact.Configure(rope.key, rope.isCable, rope.idA, rope.idB);
                }
            }

            // Try to reconstruct a hidden dynamic rope, resolving endpoints by ID or proximity
            {
                Transform aT = !string.IsNullOrEmpty(rope.idA) ? GetEndpointTransform(rope.idA) : null;
                Transform bT = !string.IsNullOrEmpty(rope.idB) ? GetEndpointTransform(rope.idB) : null;

                // Back-compat fallback: infer endpoints by proximity if IDs are missing or invalid
                if ((aT == null || bT == null) && rope.positions != null && rope.positions.Length >= 2)
                {
                    Vector3 pStart = rope.positions[0];
                    Vector3 pEnd = rope.positions[rope.positions.Length - 1];

                    Transform monNearStart = FindNearestMonitor(pStart, 3f);
                    Transform monNearEnd = FindNearestMonitor(pEnd, 3f);
                    Transform mgNearStart = FindNearestMachineOrGenerator(pStart, 3f);
                    Transform mgNearEnd = FindNearestMachineOrGenerator(pEnd, 3f);

                    if (aT == null && bT == null)
                    {
                        if (monNearStart != null && mgNearEnd != null) { aT = monNearStart; bT = mgNearEnd; }
                        else if (mgNearStart != null && monNearEnd != null) { aT = mgNearStart; bT = monNearEnd; }
                        else if (mgNearStart != null && mgNearEnd != null) { aT = mgNearStart; bT = mgNearEnd; }
                        else if (monNearStart != null && monNearEnd != null) { aT = monNearStart; bT = monNearEnd; }
                    }
                    else
                    {
                        if (aT == null)
                        {
                            aT = (monNearStart ?? mgNearStart) ?? (monNearEnd ?? mgNearEnd);
                        }
                        if (bT == null)
                        {
                            bT = (monNearEnd ?? mgNearEnd) ?? (monNearStart ?? mgNearStart);
                        }
                    }
                }
                if (aT != null && bT != null)
                {
                    if (debugLogs) Debug.Log($"[StaticRopeLoader] Resolved endpoints for {rope.key}: A={aT?.name} B={bT?.name}");
                    // Create a hidden dynamic rope that can reactivate when endpoints move
                    GameObject dyn = new GameObject($"LoadedCableDynamic_{rope.idA}_{rope.idB}");
                    var cable = dyn.AddComponent<CableComponent>();
                    // Prevent any simulation before we freeze with watcher
                    cable.enabled = false;

                    // Build proper anchors at the original connection locations
                    Transform attachA = GetAttachmentPoint(aT);
                    Transform attachB = GetAttachmentPoint(bT);
                    // Anchor at current attachment points to match present device positions
                    Vector3 startPos = attachA != null ? attachA.position : rope.positions[0];
                    Vector3 endPos = attachB != null ? attachB.position : rope.positions[rope.positions.Length - 1];
                    GameObject startAnchor = CreateCableAnchor("StartAnchor", startPos, attachA);
                    GameObject endAnchor = CreateCableAnchor("EndAnchor", endPos, attachB);
                    if (debugLogs)
                    {
                        Debug.Log($"[StaticRopeLoader] Created anchors for {rope.key}: start={startAnchor.transform.position} end={endAnchor.transform.position} attachA={attachA?.name} attachB={attachB?.name}");
                    }

                    // Assign anchors to cable endpoints
                    cable.StartPoint = startAnchor.transform;
                    cable.EndPoint = endAnchor.transform;

                    // Configure visuals/physics to match saved appearance
                    int segments = Mathf.Clamp(rope.positions.Length - 1, 2, 100);
                    CableAdapter.TrySetTotalSegments(cable, segments);
                    CableAdapter.TrySetRadius(cable, rope.radius);

                    // Choose a safe cable length
                    float savedPathLen = 0f;
                    try
                    {
                        for (int i = 0; i < rope.positions.Length - 1; i++)
                            savedPathLen += Vector3.Distance(rope.positions[i], rope.positions[i + 1]);
                    }
                    catch { /* ignore */ }
                    float endpointsDist = Vector3.Distance(startAnchor.transform.position, endAnchor.transform.position);
                    float initialLen = Mathf.Max(endpointsDist * 1.3f, savedPathLen * 1.05f);
                    CableAdapter.TrySetCableLength(cable, initialLen);
                    if (debugLogs)
                    {
                        Debug.Log($"[StaticRopeLoader] Rope {rope.key}: savedPathLen={savedPathLen:F2} endpointsDist={endpointsDist:F2} initialLen={initialLen:F2}");
                    }

                    // Set material to loader's ropeMaterial
                    var mr = dyn.GetComponent<MeshRenderer>();
                    if (mr != null && ropeMaterial != null) mr.sharedMaterial = ropeMaterial;

                    // Add capture component for persistence
                    var capture = dyn.AddComponent<RopeStaticMeshCapture>();
                    // Mark as loaded rope to prevent unnecessary recapture
                    var isLoadedField = typeof(RopeStaticMeshCapture).GetField("isLoadedRope", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    isLoadedField?.SetValue(capture, true);
                    
                    // Add stabilizer to prevent instant breaking
                    dyn.AddComponent<CableStabilizer>();
                    
                    // ALWAYS add auto-freeze and watcher, even for monitor cables
                    // This ensures proper persistence and prevents deletion
                    var autoFreeze = dyn.AddComponent<RopeAutoFreeze>();
                    var watcher = dyn.AddComponent<RopeReactivationWatcher>();

                    // Use deterministic key for re-saves
                    capture.SetCustomPrefsKey(rope.key);

                    // Register with power grid (monitors participate in the grid)
                    if (PowerSystem.Instance != null)
                    {
                        PowerSystem.Instance.AddCableConnection(dyn, attachA, attachB);
                        if (debugLogs) Debug.Log($"[StaticRopeLoader] Registered cable with PowerSystem for {rope.key}");
                    }

                    // Initialize watcher in frozen state referencing the loaded static mesh
                    // Arm immediately; freeze cooldown still prevents instant retrigger
                    watcher.Init(cable, staticObj, 0.05f, 0.1f, true);
                    
                    // Never delete persistence on an unexpected break after load
                    var removeField = typeof(RopeReactivationWatcher).GetField("removePersistenceOnBreak", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (removeField != null) removeField.SetValue(watcher, false);
                }
                else
                {
                    if (debugLogs) Debug.Log($"[StaticRopeLoader] Endpoints not ready for {rope.key}, deferring bind");
                    // Could not resolve endpoints yet; retry a few times post-load
                    if (staticObj != null)
                        StartCoroutine(TryBindEndpointsDeferred(rope, staticObj));
                }
            }
        }
    }

    // Retry endpoint resolution for ropes that loaded before systems finished registering
    private System.Collections.IEnumerator TryBindEndpointsDeferred(MachinePersistenceManager.RopeData rope, GameObject staticObj)
    {
        float elapsed = 0f;
        const float timeout = 3.0f; // seconds
        const float step = 0.25f;
        while (elapsed < timeout)
        {
            Transform aT = !string.IsNullOrEmpty(rope.idA) ? GetEndpointTransform(rope.idA) : null;
            Transform bT = !string.IsNullOrEmpty(rope.idB) ? GetEndpointTransform(rope.idB) : null;
            // Proximity fallback for legacy/migrated saves
            if ((aT == null || bT == null) && rope.positions != null && rope.positions.Length >= 2)
            {
                Vector3 pStart = rope.positions[0];
                Vector3 pEnd = rope.positions[rope.positions.Length - 1];

                Transform monNearStart = FindNearestMonitor(pStart, 3f);
                Transform monNearEnd = FindNearestMonitor(pEnd, 3f);
                Transform mgNearStart = FindNearestMachineOrGenerator(pStart, 3f);
                Transform mgNearEnd = FindNearestMachineOrGenerator(pEnd, 3f);

                if (aT == null && bT == null)
                {
                    if (monNearStart != null && mgNearEnd != null) { aT = monNearStart; bT = mgNearEnd; }
                    else if (mgNearStart != null && monNearEnd != null) { aT = mgNearStart; bT = monNearEnd; }
                    else if (mgNearStart != null && mgNearEnd != null) { aT = mgNearStart; bT = mgNearEnd; }
                    else if (monNearStart != null && monNearEnd != null) { aT = monNearStart; bT = monNearEnd; }
                }
                else
                {
                    if (aT == null)
                    {
                        aT = (monNearStart ?? mgNearStart) ?? (monNearEnd ?? mgNearEnd);
                    }
                    if (bT == null)
                    {
                        bT = (monNearEnd ?? mgNearEnd) ?? (monNearStart ?? mgNearStart);
                    }
                }
            }
            if (aT != null && bT != null)
            {
                if (debugLogs) Debug.Log($"[StaticRopeLoader] Deferred bind succeeded for {rope.key} at t={elapsed:F2}s");
                // Bind now
                GameObject dyn = new GameObject($"LoadedCableDynamic_{rope.idA}_{rope.idB}");
                var cable = dyn.AddComponent<CableComponent>();
                cable.enabled = false;

                Transform attachA = GetAttachmentPoint(aT);
                Transform attachB = GetAttachmentPoint(bT);
                Vector3 startPos = attachA != null ? attachA.position : rope.positions[0];
                Vector3 endPos = attachB != null ? attachB.position : rope.positions[rope.positions.Length - 1];
                GameObject startAnchor = CreateCableAnchor("StartAnchor", startPos, attachA);
                GameObject endAnchor = CreateCableAnchor("EndAnchor", endPos, attachB);

                cable.StartPoint = startAnchor.transform;
                cable.EndPoint = endAnchor.transform;

                int segments = Mathf.Clamp(rope.positions.Length - 1, 2, 100);
                CableAdapter.TrySetTotalSegments(cable, segments);
                CableAdapter.TrySetRadius(cable, rope.radius);

                float savedPathLen = 0f;
                try { for (int i = 0; i < rope.positions.Length - 1; i++) savedPathLen += Vector3.Distance(rope.positions[i], rope.positions[i + 1]); } catch { }
                float endpointsDist = Vector3.Distance(startAnchor.transform.position, endAnchor.transform.position);
                float initialLen = Mathf.Max(endpointsDist * 1.3f, savedPathLen * 1.05f);
                CableAdapter.TrySetCableLength(cable, initialLen);

                var mr = dyn.GetComponent<MeshRenderer>();
                if (mr != null && ropeMaterial != null) mr.sharedMaterial = ropeMaterial;

                var capture = dyn.AddComponent<RopeStaticMeshCapture>();
                // Mark as loaded rope to prevent unnecessary recapture
                var isLoadedField = typeof(RopeStaticMeshCapture).GetField("isLoadedRope", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                isLoadedField?.SetValue(capture, true);
                dyn.AddComponent<CableStabilizer>();
                
                // ALWAYS add auto-freeze and watcher for proper persistence
                var autoFreeze = dyn.AddComponent<RopeAutoFreeze>();
                var watcher = dyn.AddComponent<RopeReactivationWatcher>();

                capture.SetCustomPrefsKey(rope.key);

                if (PowerSystem.Instance != null)
                {
                    PowerSystem.Instance.AddCableConnection(dyn, attachA, attachB);
                    if (debugLogs) Debug.Log($"[StaticRopeLoader] Registered cable with PowerSystem (deferred) for {rope.key}");
                }

                // Arm immediately on deferred bind as well
                watcher.Init(cable, staticObj, 0.05f, 0.1f, true);
                var removeField = typeof(RopeReactivationWatcher).GetField("removePersistenceOnBreak", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (removeField != null) removeField.SetValue(watcher, false);
                
                yield break;
            }
            yield return new WaitForSeconds(step);
            elapsed += step;
        }
        if (debugLogs) Debug.Log($"[StaticRopeLoader] Deferred bind FAILED for {rope.key} after {timeout}s");
    }

    // [Rest of the methods remain exactly the same - no changes needed]
    
    [ContextMenu("Debug: List Saved Ropes")]
    public void DebugListRopeKeys()
    {
        var mgr = MachinePersistenceManager.Instance;
        if (mgr == null) { Debug.Log("No MachinePersistenceManager found"); return; }
        var ropes = mgr.GetRopes();
        Debug.Log($"Saved ropes: {ropes.Count}");
        foreach (var rope in ropes)
        {
            Debug.Log($"- {rope.key} | isCable={rope.isCable} | points={rope.positions?.Length ?? 0}");
        }
    }
    
    [ContextMenu("Purge Invalid Saved Ropes")] 
    public void PurgeInvalidSavedRopes()
    {
        var mgr = MachinePersistenceManager.Instance;
        if (mgr == null) return;
        var ropes = mgr.GetRopes();
        int removed = 0;
        foreach (var rope in new List<MachinePersistenceManager.RopeData>(ropes))
        {
            if (rope.isCable && !EndpointsExist(rope.idA, rope.idB))
            {
                mgr.RemoveRope(rope.key);
                removed++;
            }
        }
        Debug.Log($"Purged {removed} invalid saved rope entries");
    }

    [ContextMenu("Purge Scene Orphan Static Ropes")] 
    public void PurgeSceneOrphans()
    {
        HashSet<string> expected = new HashSet<string>();
        foreach (var go in loadedRopes)
        {
            if (go != null) expected.Add(go.name);
        }

        int removed = 0;
        var allTransforms = GameObject.FindObjectsOfType<Transform>();
        foreach (var t in allTransforms)
        {
            if (t == null) continue;
            string n = t.gameObject.name;
            if ((n.StartsWith("StaticRope_") || n.StartsWith("LoadedRope_")) && !expected.Contains(n))
            {
                if (t.gameObject == this.gameObject) continue;
                if (Application.isPlaying) Destroy(t.gameObject); else DestroyImmediate(t.gameObject);
                removed++;
            }
        }
        if (removed > 0)
        {
            Debug.Log($"Purged {removed} orphan static rope objects from the scene");
        }
    }

    private string CanonicalPair(string a, string b)
    {
        return string.CompareOrdinal(a, b) <= 0 ? $"{a}|{b}" : $"{b}|{a}";
    }

    private bool EndpointsExist(string idA, string idB)
    {
        if (PowerSystem.Instance == null) return true;

        string typeA = null, rawA = idA;
        string typeB = null, rawB = idB;
        if (!string.IsNullOrEmpty(idA) && idA.Contains(":")) { typeA = idA.Substring(0, idA.IndexOf(':') + 1); rawA = idA.Substring(idA.IndexOf(':') + 1); }
        if (!string.IsNullOrEmpty(idB) && idB.Contains(":")) { typeB = idB.Substring(0, idB.IndexOf(':') + 1); rawB = idB.Substring(idB.IndexOf(':') + 1); }

        bool aExists = false, bExists = false;

        if (typeA == "M:" || typeA == null)
        {
            aExists |= PowerSystem.Instance.GetMachineById(rawA) != null;
            if (!aExists)
            {
                string namePrefix = rawA;
                int us = rawA != null ? rawA.IndexOf('_') : -1;
                if (us > 0) namePrefix = rawA.Substring(0, us);
                foreach (var m in PowerSystem.Instance.GetAllMachines())
                {
                    if (m == null) continue;
                    if (m.MachineName == namePrefix || m.gameObject.name == namePrefix)
                    { aExists = true; break; }
                }
            }
        }
        if (typeA == "G:" || typeA == null)
        {
            aExists |= PowerSystem.Instance.GetGeneratorById(rawA) != null;
            if (!aExists)
            {
                string namePrefix = rawA;
                int us = rawA != null ? rawA.IndexOf('_') : -1;
                if (us > 0) namePrefix = rawA.Substring(0, us);
                foreach (var g in PowerSystem.Instance.GetAllGenerators())
                {
                    if (g == null) continue;
                    if (g.GeneratorName == namePrefix || g.gameObject.name == namePrefix)
                    { aExists = true; break; }
                }
            }
        }
        if (typeA == "MONID:")
            aExists |= (PowerSystem.Instance.GetMonitorById(rawA) != null);
        if (typeA == "MON:")
        {
            var mons = GameObject.FindObjectsOfType<MachineMonitor>();
            foreach (var mon in mons) { if (mon != null && mon.gameObject.name == rawA) { aExists = true; break; } }
        }

        if (typeB == "M:" || typeB == null)
        {
            bExists |= PowerSystem.Instance.GetMachineById(rawB) != null;
            if (!bExists)
            {
                string namePrefix = rawB;
                int us = rawB != null ? rawB.IndexOf('_') : -1;
                if (us > 0) namePrefix = rawB.Substring(0, us);
                foreach (var m in PowerSystem.Instance.GetAllMachines())
                {
                    if (m == null) continue;
                    if (m.MachineName == namePrefix || m.gameObject.name == namePrefix)
                    { bExists = true; break; }
                }
            }
        }
        if (typeB == "G:" || typeB == null)
        {
            bExists |= PowerSystem.Instance.GetGeneratorById(rawB) != null;
            if (!bExists)
            {
                string namePrefix = rawB;
                int us = rawB != null ? rawB.IndexOf('_') : -1;
                if (us > 0) namePrefix = rawB.Substring(0, us);
                foreach (var g in PowerSystem.Instance.GetAllGenerators())
                {
                    if (g == null) continue;
                    if (g.GeneratorName == namePrefix || g.gameObject.name == namePrefix)
                    { bExists = true; break; }
                }
            }
        }
        if (typeB == "MONID:")
            bExists |= (PowerSystem.Instance.GetMonitorById(rawB) != null);
        if (typeB == "MON:")
        {
            var monsB = GameObject.FindObjectsOfType<MachineMonitor>();
            foreach (var mon in monsB) { if (mon != null && mon.gameObject.name == rawB) { bExists = true; break; } }
        }

        return aExists && bExists;
    }

    private Transform GetEndpointTransform(string id)
    {
        if (string.IsNullOrEmpty(id)) return null;
        if (id.StartsWith("M:"))
        {
            string mid = id.Substring(2);
            if (PowerSystem.Instance != null)
            {
                var m = PowerSystem.Instance.GetMachineById(mid);
                if (m != null) return m.transform;
                string namePrefix = mid;
                int us = mid.IndexOf('_'); if (us > 0) namePrefix = mid.Substring(0, us);
                foreach (var mm in PowerSystem.Instance.GetAllMachines())
                {
                    if (mm == null) continue;
                    if (mm.MachineName == namePrefix || mm.gameObject.name == namePrefix)
                        return mm.transform;
                }
            }
            return null;
        }
        if (id.StartsWith("G:"))
        {
            string gid = id.Substring(2);
            if (PowerSystem.Instance != null)
            {
                var g = PowerSystem.Instance.GetGeneratorById(gid);
                if (g != null) return g.transform;
                string namePrefix = gid;
                int us = gid.IndexOf('_'); if (us > 0) namePrefix = gid.Substring(0, us);
                foreach (var gg in PowerSystem.Instance.GetAllGenerators())
                {
                    if (gg == null) continue;
                    if (gg.GeneratorName == namePrefix || gg.gameObject.name == namePrefix)
                        return gg.transform;
                }
            }
            return null;
        }
        if (id.StartsWith("MONID:"))
        {
            string monId = id.Substring(6);
            if (PowerSystem.Instance != null)
            {
                var mon = PowerSystem.Instance.GetMonitorById(monId);
                if (mon != null) return mon.transform;
                foreach (var mm in PowerSystem.Instance.GetAllMonitors())
                {
                    if (mm == null) continue;
                    if (mm.gameObject != null && mm.gameObject.name == monId)
                        return mm.transform;
                }
            }
            return null;
        }
        if (id.StartsWith("MON:"))
        {
            string name = id.Substring(4);
            var mons = GameObject.FindObjectsOfType<MachineMonitor>();
            foreach (var mon in mons)
            {
                if (mon != null && mon.gameObject.name == name)
                    return mon.transform;
            }
            return null;
        }
        if (PowerSystem.Instance != null)
        {
            var m = PowerSystem.Instance.GetMachineById(id);
            if (m != null) return m.transform;
            var g = PowerSystem.Instance.GetGeneratorById(id);
            if (g != null) return g.transform;
            string namePrefix = id;
            int us = id.IndexOf('_'); if (us > 0) namePrefix = id.Substring(0, us);
            foreach (var mm in PowerSystem.Instance.GetAllMachines())
            {
                if (mm == null) continue;
                if (mm.MachineName == namePrefix || mm.gameObject.name == namePrefix)
                    return mm.transform;
            }
            foreach (var gg in PowerSystem.Instance.GetAllGenerators())
            {
                if (gg == null) continue;
                if (gg.GeneratorName == namePrefix || gg.gameObject.name == namePrefix)
                    return gg.transform;
            }
        }
        return null;
    }

    private GameObject CreateCableAnchor(string name, Vector3 position, Transform attachment)
    {
        GameObject anchor = new GameObject(name);
        anchor.transform.position = position;

        if (attachment != null)
        {
            Rigidbody rb = attachment.GetComponent<Rigidbody>();
            if (rb != null)
            {
                anchor.transform.SetParent(attachment);
            }
            else
            {
                anchor.transform.position = position;
                if (!attachment.CompareTag("Player"))
                {
                    anchor.transform.SetParent(attachment);
                }
            }
        }

        return anchor;
    }

    private Transform GetAttachmentPoint(Transform endpoint)
    {
        if (endpoint == null) return null;
        var m = endpoint.GetComponentInParent<Machine>();
        if (m != null) return m.ConnectionPoint;
        var g = endpoint.GetComponentInParent<Generator>();
        if (g != null) return g.ConnectionPoint;
        var mon = endpoint.GetComponentInParent<MachineMonitor>();
        if (mon != null) return mon.transform;
        return endpoint;
    }

    private Transform FindNearestMonitor(Vector3 position, float maxDistance)
    {
        float best = maxDistance * maxDistance;
        Transform bestT = null;
        var mons = GameObject.FindObjectsOfType<MachineMonitor>();
        foreach (var mon in mons)
        {
            if (mon == null) continue;
            float d = (mon.transform.position - position).sqrMagnitude;
            if (d <= best) { best = d; bestT = mon.transform; }
        }
        return bestT;
    }

    private Transform FindNearestMachineOrGenerator(Vector3 position, float maxDistance)
    {
        float best = maxDistance * maxDistance;
        Transform bestT = null;
        if (PowerSystem.Instance != null)
        {
            var machines = PowerSystem.Instance.GetAllMachines();
            foreach (var m in machines)
            {
                if (m == null) continue;
                float d = (m.transform.position - position).sqrMagnitude;
                if (d <= best) { best = d; bestT = m.transform; }
            }
            var gens = PowerSystem.Instance.GetAllGenerators();
            foreach (var g in gens)
            {
                if (g == null) continue;
                float d = (g.transform.position - position).sqrMagnitude;
                if (d <= best) { best = d; bestT = g.transform; }
            }
        }
        return bestT;
    }
    
    private System.Collections.IEnumerator PostLoadGridRecompute()
    {
        if (postLoadGridDelay > 0f)
            yield return new WaitForSeconds(postLoadGridDelay);
        if (PowerSystem.Instance == null)
            yield break;
        try
        {
            PowerSystem.Instance.UpdatePowerGrid();
            var gens = PowerSystem.Instance.GetAllGenerators();
            foreach (var g in gens)
            {
                if (g == null) continue;
                g.StartGenerating();
            }
            PowerSystem.Instance.UpdatePowerGrid();
            if (debugLogs) Debug.Log("[StaticRopeLoader] PostLoadGridRecompute complete");
        }
        catch { }
    }
    
    private GameObject CreateStaticRope(RopeStateData data)
    {
        if (data == null || data.positions == null || data.positions.Length < 2)
        {
            Debug.LogError("Invalid rope state data!");
            return null;
        }
        
        float radius = data.radius > 0 ? data.radius : defaultRadius;
        int sides = data.sides > 0 ? data.sides : defaultSides;
        Vector3 pivot = data.positions[0];
        Vector3[] localPositions = new Vector3[data.positions.Length];
        for (int i = 0; i < data.positions.Length; i++)
            localPositions[i] = data.positions[i] - pivot;
        Mesh mesh = GenerateRopeMesh(localPositions, radius, sides);
        
        if (mesh == null) return null;
        
        GameObject ropeObj = new GameObject($"LoadedRope_{data.ropeName}");
        TryConfigureStaticLayer();
        ropeObj.layer = staticMeshLayer;
        ropeObj.transform.SetParent(transform);
        ropeObj.transform.position = pivot;
        loadedRopes.Add(ropeObj);
        
        MeshFilter meshFilter = ropeObj.AddComponent<MeshFilter>();
        MeshRenderer meshRenderer = ropeObj.AddComponent<MeshRenderer>();
        
        meshFilter.mesh = mesh;
        meshRenderer.sharedMaterial = ropeMaterial;

        try
        {
            AddSegmentTriggerColliders(ropeObj, localPositions, radius);
        }
        catch
        {
            BoxCollider bc = ropeObj.AddComponent<BoxCollider>();
            bc.center = mesh.bounds.center;
            bc.size = mesh.bounds.size;
            bc.isTrigger = true;
            bc.gameObject.layer = staticMeshLayer;
        }

        if (enableInteraction)
        {
            var interact = ropeObj.AddComponent<StaticRopeInteractable>();
            interact.ConfigureDropSettings(interactRopeItemName, interactRopeItemQuantity, consumeOnInteract);
        }

        return ropeObj;
    }

    private string MakeAssetRopeKey(RopeStateAsset asset)
    {
        if (asset == null || asset.stateData == null) return null;
        return $"AssetRope|{asset.name}|{asset.stateData.ropeName}";
    }

    private void TryConfigureStaticLayer()
    {
        if (s_StaticLayerConfigured) return;
        s_StaticLayerConfigured = true;
        for (int i = 0; i < 32; i++)
        {
            try { Physics.IgnoreLayerCollision(staticMeshLayer, i, true); } catch { }
        }
    }
    
    private Mesh GenerateRopeMesh(Vector3[] positions, float radius, int sides)
    {
        Mesh mesh = new Mesh();
        mesh.name = "StaticRopeMesh";
        
        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Vector2> uvs = new List<Vector2>();
        List<Vector3> normals = new List<Vector3>();
        
        for (int i = 0; i < positions.Length; i++)
        {
            Vector3 position = positions[i];
            
            Vector3 tangent = Vector3.zero;
            if (i > 0)
                tangent += (positions[i] - positions[i - 1]).normalized;
            if (i < positions.Length - 1)
                tangent += (positions[i + 1] - positions[i]).normalized;
            
            if (tangent.magnitude < 0.001f)
                tangent = Vector3.forward;
            else
                tangent = tangent.normalized;
            
            Vector3 up = Vector3.up;
            if (Mathf.Abs(Vector3.Dot(tangent, up)) > 0.99f)
                up = Vector3.right;
            
            Vector3 right = Vector3.Cross(tangent, up).normalized;
            up = Vector3.Cross(tangent, right).normalized;
            
            float angleStep = (2f * Mathf.PI) / sides;
            for (int j = 0; j < sides; j++)
            {
                float angle = j * angleStep;
                float x = Mathf.Cos(angle) * radius;
                float y = Mathf.Sin(angle) * radius;
                
                Vector3 vertex = position + right * x + up * y;
                vertices.Add(vertex);
                
                Vector3 normal = (vertex - position).normalized;
                normals.Add(normal);
                
                float u = (float)j / (sides - 1);
                float v = (float)i / (positions.Length - 1);
                uvs.Add(new Vector2(u, v));
            }
        }
        
        for (int segment = 0; segment < positions.Length - 1; segment++)
        {
            for (int side = 0; side < sides; side++)
            {
                int current = segment * sides + side;
                int next = current + sides;
                int currentNext = (side == sides - 1) ? (segment * sides) : (current + 1);
                int nextNext = (side == sides - 1) ? ((segment + 1) * sides) : (next + 1);
                
                triangles.Add(current);
                triangles.Add(currentNext);
                triangles.Add(next);

                triangles.Add(currentNext);
                triangles.Add(nextNext);
                triangles.Add(next);
            }
        }
        
        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.uv = uvs.ToArray();
        mesh.normals = normals.ToArray();
        
        mesh.RecalculateBounds();
        mesh.RecalculateTangents();
        
        #if UNITY_EDITOR
        if (optimizeMeshes)
        {
            mesh.Optimize();
        }
        #endif
        
        return mesh;
    }

    private void AddSegmentTriggerColliders(GameObject root, Vector3[] localPositions, float radius)
    {
        if (localPositions == null || localPositions.Length < 2) return;
        float segRadius = Mathf.Max(0.01f, radius);
        for (int i = 0; i < localPositions.Length - 1; i++)
        {
            Vector3 a = localPositions[i];
            Vector3 b = localPositions[i + 1];
            Vector3 mid = (a + b) * 0.5f;
            Vector3 dir = (b - a);
            float len = dir.magnitude;
            if (len <= 1e-4f) continue;
            dir /= len;

            var seg = new GameObject($"SegCol_{i}");
            seg.layer = root.layer;
            seg.transform.SetParent(root.transform, false);
            seg.transform.localPosition = mid;
            seg.transform.localRotation = Quaternion.FromToRotation(Vector3.up, dir);

            var cap = seg.AddComponent<CapsuleCollider>();
            cap.direction = 1;
            cap.radius = segRadius;
            cap.height = Mathf.Max(len + segRadius * 2f, segRadius * 2.1f);
            cap.isTrigger = true;
        }
    }
    
    [ContextMenu("Clear Loaded Ropes")]
    public void ClearLoadedRopes()
    {
        foreach (var rope in loadedRopes)
        {
            if (rope != null)
                DestroyImmediate(rope);
        }
        loadedRopes.Clear();
    }
    
    public void AddRopeAsset(RopeStateAsset asset)
    {
        if (!ropeAssets.Contains(asset))
        {
            ropeAssets.Add(asset);
        }
    }
    
    public void LoadSingleRope(RopeStateData data)
    {
        CreateStaticRope(data);
    }
    
    [ContextMenu("Create Test Rope Pattern")]
    private void CreateTestRopePattern()
    {
        Vector3[] testPositions = new Vector3[20];
        for (int i = 0; i < 20; i++)
        {
            float t = (float)i / 19f;
            testPositions[i] = new Vector3(
                Mathf.Sin(t * Mathf.PI * 2) * 2f,
                -t * 3f,
                Mathf.Cos(t * Mathf.PI * 2) * 2f
            );
        }
        
        RopeStateData testData = new RopeStateData(
            testPositions,
            defaultRadius,
            defaultSides,
            "TestSpiral"
        );
        
        CreateStaticRope(testData);
    }
}
