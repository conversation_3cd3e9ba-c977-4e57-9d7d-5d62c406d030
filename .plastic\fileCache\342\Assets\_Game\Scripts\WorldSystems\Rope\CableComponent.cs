using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

[RequireComponent(typeof(MeshRenderer))]
[RequireComponent(typeof(MeshFilter))]
public class CableComponent : MonoBehaviour
{
    [Header("Cable Endpoints")]
    [SerializeField] private Transform _StartPoint;
    [SerializeField] private Transform _EndPoint;
    
    [Header("Cable Properties")]
    [SerializeField] private float _CableLength = 2f;
    [SerializeField] private int _TotalSegments = 24;
    [SerializeField] private float _CableMass = 1f;
    [Range(0.1f, 1f)]
    [SerializeField] private float _Stiffness = 0.8f;
    [Range(0f, 1f)]
    [SerializeField] private float _Damping = 0.99f;
    
    [Header("Physics Simulation")]
    [SerializeField] private int _VerletIterations = 2;
    [SerializeField] private int _SolverIterations = 4;
    [SerializeField] private float _StretchingStiffness = 0.9f;
    [SerializeField] private bool _UseGravityMultiplier = false;
    [SerializeField] private float _GravityMultiplier = 1f;
    
    [Header("Collision Detection")]
    [SerializeField] private LayerMask _CollisionLayers = -1;
    [SerializeField] private float _CollisionRadius = 0.05f;
    [SerializeField] private bool _EnableCollisions = true;
    [SerializeField] private float _Friction = 0.5f;
    [SerializeField] private int _DraggedSolverIterations = 8;
    
    [Header("Advanced Physics")]
    [SerializeField] private bool _EnableWind = false;
    [SerializeField] private Vector3 _WindForce = new Vector3(1, 0, 0);
    [SerializeField] private float _WindTurbulence = 0.5f;
    [SerializeField] private bool _EnableSelfCollision = false;
    [SerializeField] private float _MinBendRadius = 0.1f;

    [Header("Rope Breaking")]
    [SerializeField] private bool _EnableBreaking = true;
    [SerializeField] private float _BreakThreshold = 2.0f; // Multiplier of cable length before breaking
    [SerializeField] private float _BreakDamping = 0.95f; // Damping when near breaking point
    [SerializeField] private GameObject _BreakEffectPrefab = null;
    [SerializeField] private AudioClip _BreakSound = null;
    
    [Header("Rendering")]
    [SerializeField] private int _Sides = 8;
    [SerializeField] private float _Radius = 0.05f;
    [SerializeField] private bool _UseLOD = true;
    [SerializeField] private float _LODDistance = 50f;
    [SerializeField] private bool _ShowCollisionDebug = false;
    
    // Public properties for external access
    public Transform StartPoint
    {
        get { return _StartPoint; }
        set { _StartPoint = value; }
    }
    
    public Transform EndPoint
    {
        get { return _EndPoint; }
        set { _EndPoint = value; }
    }
    
    public float CableLength
    {
        get { return _CableLength; }
        set { _CableLength = Mathf.Max(0.1f, value); }
    }

    public bool EnableCollisions
    {
        get { return _EnableCollisions; }
        set { _EnableCollisions = value; }
    }

    public LayerMask CollisionLayers
    {
        get { return _CollisionLayers; }
        set { _CollisionLayers = value; }
    }

    public bool EnableBreaking
    {
        get { return _EnableBreaking; }
        set { _EnableBreaking = value; }
    }

    public float BreakThreshold
    {
        get { return _BreakThreshold; }
        set { _BreakThreshold = Mathf.Max(1.1f, value); }
    }

    public delegate void RopeBreakHandler(CableComponent brokenRope, Vector3 breakPoint);
    public event RopeBreakHandler OnRopeBreak;

    // Internal state
    private CableParticle[] _Points;
    private int _Segments = 0;
    private Vector3[] _Positions;
    private float _SegmentLength;
    private float _ParticleMass;
    
    // Collision bodies for ComputePenetration
    private SphereCollider[] _ParticleColliders;
    private CapsuleCollider[] _SegmentColliders;
    private GameObject _CollisionContainer;
    
    // Mesh generation
    private Vector3[] _Vertices;
    private Vector3[] _Normals;
    private int[] _Triangles;
    private Vector2[] _UVs;
    private Mesh _Mesh;
    private MeshFilter _MeshFilter;
    private MeshRenderer _MeshRenderer;
    
    // End caps for visual polish
    private GameObject _SphereStart;
    private GameObject _SphereEnd;
    
    // Performance tracking
    private float _LastUpdateTime;
    private int _CurrentLODLevel = 0;
    
    // Wind simulation
    private float _WindPhase = 0f;

    // Rope breaking state
    private bool _IsBroken = false;
    private float _MaxStretchRatio = 0f;

    // Public configuration API (prefer using these over reflection)
    public int TotalSegments
    {
        get { return _TotalSegments; }
        set { _TotalSegments = Mathf.Clamp(value, 2, 100); }
    }

    public float Radius
    {
        get { return _Radius; }
        set { _Radius = Mathf.Clamp(value, 0.01f, 1f); }
    }

    public float CableMass
    {
        get { return _CableMass; }
        set { _CableMass = Mathf.Max(0f, value); }
    }

    public float Stiffness
    {
        get { return _Stiffness; }
        set { _Stiffness = Mathf.Clamp01(value); }
    }

    /// <summary>
    /// Current effective segment count (initialized value when running, otherwise desired total).
    /// </summary>
    public int GetSegmentCount()
    {
        return Mathf.Max(2, _Segments > 0 ? _Segments : _TotalSegments);
    }

    /// <summary>
    /// Configure core physics parameters. Optionally reinitialize immediately.
    /// </summary>
    public void ConfigurePhysics(int segments, float radius, float mass, float stiffness, bool reinitialize = false)
    {
        TotalSegments = segments;
        Radius = radius;
        CableMass = mass;
        Stiffness = stiffness;
        if (reinitialize)
        {
            ReinitializeCable();
        }
    }

    #region MONOBEHAVIOUR

    void Start()
    {
        InitializePhysics();
        InitializeRenderer();
        InitializeCollisionBodies();

        // Ensure visuals are cleared instantly on break
        OnRopeBreak += HandleSelfBreakVisuals;
    }

    void Update()
    {
        if (_Points != null && _Points.Length > 0 && !_IsBroken)
        {
            UpdateLOD();
            RenderCable();

            if (_ShowCollisionDebug)
            {
                DebugDrawCollisions();
            }
        }
    }

    void FixedUpdate()
    {
        if (_Points != null && _Points.Length > 0 && !_IsBroken)
        {
            // Detect if rope is being dragged
            bool isBeingDragged = false;
            if (_StartPoint != null && _EndPoint != null)
            {
                float startMovement = Vector3.Distance(_Points[0].Position, _StartPoint.position);
                float endMovement = Vector3.Distance(_Points[_Segments].Position, _EndPoint.position);
                isBeingDragged = (startMovement > _CollisionRadius * 3f || endMovement > _CollisionRadius * 3f);
            }
            
            int actualSolverIterations = isBeingDragged ? _DraggedSolverIterations : _SolverIterations;
            
            // Update wind phase for turbulence
            if (_EnableWind)
            {
                _WindPhase += Time.fixedDeltaTime * _WindTurbulence;
            }
            
            // Run physics simulation
            for (int verletIdx = 0; verletIdx < _VerletIterations; verletIdx++)
            {
                VerletIntegrate();

                // Run constraint solver with integrated collision resolution
                int savedIterations = _SolverIterations;
                _SolverIterations = actualSolverIterations;
                SolveConstraintsWithCollisions();
                _SolverIterations = savedIterations;
            }

            // Check for rope breaking
            if (_EnableBreaking && !_IsBroken)
            {
                CheckForBreaking();
            }

            _LastUpdateTime = Time.time;
        }
    }
    
    void OnValidate()
    {
        _TotalSegments = Mathf.Clamp(_TotalSegments, 2, 100);
        _CableLength = Mathf.Max(0.1f, _CableLength);
        _CollisionRadius = Mathf.Clamp(_CollisionRadius, 0.01f, 1f);
        _Radius = Mathf.Clamp(_Radius, 0.01f, 1f);
        _Sides = Mathf.Clamp(_Sides, 3, 16);
        _BreakThreshold = Mathf.Max(1.1f, _BreakThreshold);
    }

    void OnDestroy()
    {
        OnRopeBreak -= HandleSelfBreakVisuals;
        if (_CollisionContainer != null)
        {
            if (Application.isPlaying) Destroy(_CollisionContainer); else DestroyImmediate(_CollisionContainer);
        }
    }

    #endregion

    private void HandleSelfBreakVisuals(CableComponent broken, Vector3 breakPoint)
    {
        // Mark as broken
        _IsBroken = true;

        // Disable rendering immediately to avoid a frozen rope ghost
        try
        {
            if (_MeshRenderer == null) _MeshRenderer = GetComponent<MeshRenderer>();
            if (_MeshFilter == null) _MeshFilter = GetComponent<MeshFilter>();
            if (_MeshRenderer != null) _MeshRenderer.enabled = false;
            if (_SphereStart != null) _SphereStart.SetActive(false);
            if (_SphereEnd != null) _SphereEnd.SetActive(false);
            if (_Mesh != null)
            {
                _Mesh.Clear();
            }
            else if (_MeshFilter != null && _MeshFilter.sharedMesh != null)
            {
                _MeshFilter.sharedMesh = new Mesh();
            }
        }
        catch { }

        // Disable collisions for this rope
        try
        {
            if (_CollisionContainer != null)
            {
                _CollisionContainer.SetActive(false);
            }
        }
        catch { }

        // Stop further simulation
        enabled = false;
    }

    #region INITIALIZATION

    void InitializePhysics()
    {
        if (_StartPoint == null || _EndPoint == null)
        {
            Debug.LogWarning("CableComponent: StartPoint or EndPoint is null.");
            return;
        }

        _Segments = _TotalSegments;
        _SegmentLength = _CableLength / _Segments;
        _ParticleMass = _CableMass / (_Segments + 1);
        
        Vector3 start = _StartPoint.position;
        Vector3 end = _EndPoint.position;
        Vector3 delta = end - start;
        float horizontalDistance = new Vector3(delta.x, 0, delta.z).magnitude;
        
        _Points = new CableParticle[_Segments + 1];
        
        for (int i = 0; i <= _Segments; i++)
        {
            float t = (float)i / _Segments;
            Vector3 position = Vector3.Lerp(start, end, t);
            
            // Add natural sag
            if (horizontalDistance > 0.1f)
            {
                float sagAmount = (_CableLength - delta.magnitude) * 0.5f;
                sagAmount = Mathf.Max(0, sagAmount);
                float sagCurve = 4f * t * (1f - t);
                position.y -= sagCurve * sagAmount;
            }
            
            _Points[i] = new CableParticle(position);
            _Points[i].InverseMass = 1f / _ParticleMass;
        }
        
        // Bind endpoints
        _Points[0].Bind(_StartPoint);
        _Points[_Segments].Bind(_EndPoint);
        
        // Set middle particles to have slightly more mass for stability
        for (int i = _Segments / 3; i <= 2 * _Segments / 3; i++)
        {
            _Points[i].InverseMass = 1f / (_ParticleMass * 1.2f);
        }
    }
    
    void InitializeCollisionBodies()
    {
        if (!_EnableCollisions) return;
        
        // Create container for collision bodies
        _CollisionContainer = new GameObject($"{gameObject.name}_CollisionBodies");
        _CollisionContainer.transform.SetParent(transform);
        
        // Create sphere colliders for particles
        _ParticleColliders = new SphereCollider[_Segments + 1];
        for (int i = 0; i <= _Segments; i++)
        {
            GameObject particleObj = new GameObject($"Particle_{i}");
            particleObj.transform.SetParent(_CollisionContainer.transform);
            particleObj.transform.position = _Points[i].Position;
            
            SphereCollider sphere = particleObj.AddComponent<SphereCollider>();
            sphere.radius = _CollisionRadius;
            sphere.isTrigger = true;
            _ParticleColliders[i] = sphere;
        }
        
        // Create capsule colliders for segments
        _SegmentColliders = new CapsuleCollider[_Segments];
        for (int i = 0; i < _Segments; i++)
        {
            GameObject segmentObj = new GameObject($"Segment_{i}");
            segmentObj.transform.SetParent(_CollisionContainer.transform);
            
            CapsuleCollider capsule = segmentObj.AddComponent<CapsuleCollider>();
            capsule.radius = _CollisionRadius;
            capsule.isTrigger = true;
            capsule.direction = 2; // Z-axis
            _SegmentColliders[i] = capsule;
        }
    }
    
    public void ReinitializeCable()
    {
        InitializePhysics();
        if (_EnableCollisions)
        {
            if (_CollisionContainer != null)
            {
                if (Application.isPlaying) Destroy(_CollisionContainer); else DestroyImmediate(_CollisionContainer);
            }
            InitializeCollisionBodies();
        }
    }

    #endregion

    #region PHYSICS SIMULATION

    void VerletIntegrate()
    {
        // Calculate gravity
        Vector3 gravity = Physics.gravity;
        if (_UseGravityMultiplier)
        {
            gravity *= _GravityMultiplier;
        }
        Vector3 gravityDisplacement = Time.fixedDeltaTime * Time.fixedDeltaTime * gravity;
        
        // Update each particle - NO collision handling here anymore
        for (int i = 0; i <= _Segments; i++)
        {
            CableParticle particle = _Points[i];
            
            // Apply wind force if enabled
            if (_EnableWind && particle.IsFree())
            {
                Vector3 wind = CalculateWindForce(particle.Position, i);
                particle.ApplyForce(wind);
            }

            // Apply air resistance
            if (particle.IsFree())
            {
                Vector3 velocity = particle.Velocity;
                Vector3 airResistance = -velocity * velocity.magnitude * 0.01f * Time.fixedDeltaTime;
                particle.ApplyForce(airResistance);
            }
            
            // Simple Verlet integration - no collision resolution
            particle.UpdateVerletSimple(gravityDisplacement, _Damping);
        }
    }
    
    Vector3 CalculateWindForce(Vector3 position, int particleIndex)
    {
        Vector3 wind = _WindForce;
        
        float noiseX = Mathf.PerlinNoise(_WindPhase + particleIndex * 0.1f, 0f) - 0.5f;
        float noiseY = Mathf.PerlinNoise(0f, _WindPhase + particleIndex * 0.1f) - 0.5f;
        float noiseZ = Mathf.PerlinNoise(_WindPhase + particleIndex * 0.1f, _WindPhase) - 0.5f;
        
        wind += new Vector3(noiseX, noiseY, noiseZ) * _WindTurbulence * wind.magnitude;
        return wind;
    }

    void SolveConstraintsWithCollisions()
    {
        // Update collision body positions
        if (_EnableCollisions)
        {
            UpdateCollisionBodies();
        }
        
        // Multiple constraint iterations with integrated collision resolution
        for (int iteration = 0; iteration < _SolverIterations; iteration++)
        {
            // CHANGE 1: Collision resolution in every solver iteration
            if (_EnableCollisions)
            {
                SolveCollisionConstraints();
            }
            
            // Distance constraints with tangent plane projection
            bool forward = (iteration % 2) == 0;
            if (forward)
            {
                for (int i = 0; i < _Segments; i++)
                {
                    SolveDistanceConstraintWithSurfaceProjection(i, i + 1);
                    
                    if (i < _Segments - 1)
                    {
                        SolveBendingConstraint(i, i + 1, i + 2);
                    }
                }
            }
            else
            {
                for (int i = _Segments - 1; i >= 0; i--)
                {
                    SolveDistanceConstraintWithSurfaceProjection(i, i + 1);
                    
                    if (i > 0)
                    {
                        SolveBendingConstraint(i - 1, i, i + 1);
                    }
                }
            }
            
            // Self-collision if enabled
            if (_EnableSelfCollision && iteration == _SolverIterations - 1)
            {
                SolveSelfCollision();
            }
        }
    }
    
    void UpdateCollisionBodies()
    {
        // Update particle colliders
        for (int i = 0; i <= _Segments; i++)
        {
            if (_ParticleColliders[i] != null)
            {
                _ParticleColliders[i].transform.position = _Points[i].Position;
            }
        }
        
        // Update segment colliders
        for (int i = 0; i < _Segments; i++)
        {
            if (_SegmentColliders[i] != null)
            {
                Vector3 start = _Points[i].Position;
                Vector3 end = _Points[i + 1].Position;
                Vector3 center = (start + end) * 0.5f;
                Vector3 direction = end - start;
                float length = direction.magnitude;
                
                _SegmentColliders[i].transform.position = center;
                _SegmentColliders[i].transform.rotation = Quaternion.LookRotation(direction.normalized);
                _SegmentColliders[i].height = length + _CollisionRadius * 2f; // Account for sphere caps
            }
        }
    }
    
    void SolveCollisionConstraints()
    {
        // CHANGE 2: Use ComputePenetration for accurate collision data
        for (int i = 0; i <= _Segments; i++)
        {
            if (!_Points[i].IsFree()) continue;
            
            SphereCollider particleCollider = _ParticleColliders[i];
            if (particleCollider == null) continue;
            
            _Points[i].ClearCollisionInfo();
            
            // Find all overlapping colliders
            Collider[] overlaps = Physics.OverlapSphere(_Points[i].Position, _CollisionRadius, _CollisionLayers);
            
            Vector3 totalCorrection = Vector3.zero;
            Vector3 averageNormal = Vector3.zero;
            int collisionCount = 0;
            
            foreach (Collider other in overlaps)
            {
                // Skip our own collision bodies
                if (other.transform.IsChildOf(_CollisionContainer.transform))
                    continue;
                
                Vector3 direction;
                float distance;
                bool hasPenetration = Physics.ComputePenetration(
                    particleCollider, _Points[i].Position, Quaternion.identity,
                    other, other.transform.position, other.transform.rotation,
                    out direction, out distance
                );
                
                if (hasPenetration)
                {
                    // Apply immediate position correction
                    Vector3 correction = direction * distance;
                    totalCorrection += correction;
                    averageNormal += direction;
                    collisionCount++;
                }
                else
                {
                    // Fallback to ClosestPoint method
                    Vector3 closestPoint = other.ClosestPoint(_Points[i].Position);
                    Vector3 toParticle = _Points[i].Position - closestPoint;
                    float dist = toParticle.magnitude;
                    
                    if (dist < _CollisionRadius && dist > 0.001f)
                    {
                        Vector3 correction = toParticle.normalized * (_CollisionRadius - dist);
                        totalCorrection += correction;
                        averageNormal += toParticle.normalized;
                        collisionCount++;
                    }
                }
            }
            
            if (collisionCount > 0)
            {
                // Apply averaged correction
                _Points[i].Position += totalCorrection;
                
                // Store collision info for constraint projection
                averageNormal = (averageNormal / collisionCount).normalized;
                _Points[i].SetCollisionInfo(averageNormal, _Friction);
                
                // Apply velocity damping for collision response
                _Points[i].ApplyCollisionDamping(averageNormal, 0.2f, _Friction);
            }
        }
    }
    
    void SolveDistanceConstraintWithSurfaceProjection(int indexA, int indexB)
    {
        // CHANGE 3: Project corrections onto surface tangent planes
        CableParticle particleA = _Points[indexA];
        CableParticle particleB = _Points[indexB];
        
        Vector3 delta = particleB.Position - particleA.Position;
        float currentDistance = delta.magnitude;
        
        if (currentDistance < 0.0001f) return;
        
        float targetDistance = _SegmentLength;
        float stretchRatio = (currentDistance - targetDistance) / currentDistance;
        stretchRatio *= _StretchingStiffness * _Stiffness;
        
        float totalInverseMass = particleA.InverseMass + particleB.InverseMass;
        if (totalInverseMass < 0.0001f) return;
        
        float weightA = particleA.InverseMass / totalInverseMass;
        float weightB = particleB.InverseMass / totalInverseMass;
        
        Vector3 correction = delta * stretchRatio;
        Vector3 correctionA = correction * weightA;
        Vector3 correctionB = -correction * weightB;
        
        // Project corrections onto surface tangent planes if particles are colliding
        if (particleA.IsFree() && particleA.IsColliding())
        {
            Vector3 surfaceNormal = particleA.GetCollisionNormal();
            correctionA = ProjectOntoTangentPlane(correctionA, surfaceNormal);
        }
        
        if (particleB.IsFree() && particleB.IsColliding())
        {
            Vector3 surfaceNormal = particleB.GetCollisionNormal();
            correctionB = ProjectOntoTangentPlane(correctionB, surfaceNormal);
        }
        
        // Apply corrections
        if (particleA.IsFree())
            particleA.Position += correctionA;
        if (particleB.IsFree())
            particleB.Position += correctionB;
    }
    
    Vector3 ProjectOntoTangentPlane(Vector3 vector, Vector3 surfaceNormal)
    {
        // Remove the component of the vector that points into the surface
        float normalComponent = Vector3.Dot(vector, surfaceNormal);
        if (normalComponent < 0) // Only project if vector points into surface
        {
            return vector - surfaceNormal * normalComponent;
        }
        return vector;
    }
    
    void SolveBendingConstraint(int indexA, int indexB, int indexC)
    {
        CableParticle particleA = _Points[indexA];
        CableParticle particleB = _Points[indexB];
        CableParticle particleC = _Points[indexC];

        Vector3 ba = particleA.Position - particleB.Position;
        Vector3 bc = particleC.Position - particleB.Position;

        float angle = Vector3.Angle(ba, bc);
        float segmentLength = _SegmentLength;
        float maxAngle = 2f * Mathf.Asin(segmentLength / (2f * _MinBendRadius)) * Mathf.Rad2Deg;
        maxAngle = Mathf.Clamp(maxAngle, 90f, 175f);

        if (angle > maxAngle)
        {
            Vector3 midPoint = (particleA.Position + particleC.Position) * 0.5f;
            Vector3 toBend = midPoint - particleB.Position;
            float bendCorrection = (angle - maxAngle) / 180f * 0.1f;

            if (particleB.IsFree())
            {
                Vector3 correction = toBend * bendCorrection;
                
                // Project onto tangent plane if colliding
                if (particleB.IsColliding())
                {
                    Vector3 surfaceNormal = particleB.GetCollisionNormal();
                    correction = ProjectOntoTangentPlane(correction, surfaceNormal);
                }
                
                particleB.Position += correction;
            }
        }
    }
    
    void SolveSelfCollision()
    {
        float minDistance = _CollisionRadius * 2f;

        for (int i = 0; i < _Segments; i++)
        {
            for (int j = i + 2; j <= _Segments; j++)
            {
                CableParticle p1 = _Points[i];
                CableParticle p2 = _Points[j];

                if (!p1.IsFree() && !p2.IsFree()) continue;

                Vector3 delta = p2.Position - p1.Position;
                float distance = delta.magnitude;

                if (distance < minDistance && distance > 0.0001f)
                {
                    Vector3 correction = delta.normalized * (minDistance - distance) * 0.5f;

                    // Project corrections if particles are colliding with surfaces
                    if (p1.IsFree())
                    {
                        Vector3 correctionA = -correction;
                        if (p1.IsColliding())
                        {
                            correctionA = ProjectOntoTangentPlane(correctionA, p1.GetCollisionNormal());
                        }
                        p1.Position += correctionA;
                    }

                    if (p2.IsFree())
                    {
                        Vector3 correctionB = correction;
                        if (p2.IsColliding())
                        {
                            correctionB = ProjectOntoTangentPlane(correctionB, p2.GetCollisionNormal());
                        }
                        p2.Position += correctionB;
                    }
                }
            }
        }
    }

    void CheckForBreaking()
    {
        if (_IsBroken || _Points == null) return;

        // Check the overall distance between endpoints
        Vector3 startPos = _Points[0].Position;
        Vector3 endPos = _Points[_Segments].Position;
        float currentLength = Vector3.Distance(startPos, endPos);
        float stretchRatio = currentLength / _CableLength;

        // Update max stretch ratio for monitoring
        _MaxStretchRatio = Mathf.Max(_MaxStretchRatio, stretchRatio);

        // Check if rope should break
        if (stretchRatio >= _BreakThreshold)
        {
            BreakRope((startPos + endPos) * 0.5f);
            return;
        }

        // Apply damping when approaching break threshold
        float dampingThreshold = _BreakThreshold * 0.8f;
        if (stretchRatio >= dampingThreshold)
        {
            float dampingFactor = Mathf.Lerp(1f, _BreakDamping, (stretchRatio - dampingThreshold) / (dampingThreshold * 0.2f));

            // Apply damping to all particles
            for (int i = 0; i <= _Segments; i++)
            {
                if (_Points[i].IsFree())
                {
                    _Points[i].ApplyForce(-_Points[i].Velocity * (1f - dampingFactor) * 0.1f);
                }
            }
        }
    }

    public void BreakRope(Vector3 breakPoint)
    {
        if (_IsBroken) return;

        _IsBroken = true;
        Debug.Log($"Rope broke at stretch ratio: {_MaxStretchRatio:F2}x (threshold: {_BreakThreshold}x)");

        // Trigger break event
        OnRopeBreak?.Invoke(this, breakPoint);

        // Play break effect
        if (_BreakEffectPrefab != null)
        {
            Instantiate(_BreakEffectPrefab, breakPoint, Quaternion.identity);
        }

        // Play break sound
        if (_BreakSound != null)
        {
            AudioSource.PlayClipAtPoint(_BreakSound, breakPoint);
        }

        // Disable physics updates
        enabled = false;

        // Optional: Destroy the rope after a delay
        Destroy(gameObject, 1f);
    }

    public bool IsBroken()
    {
        return _IsBroken;
    }

    public float GetMaxStretchRatio()
    {
        return _MaxStretchRatio;
    }

    #endregion

    #region RENDERING

    void InitializeRenderer()
    {
        _MeshFilter = GetComponent<MeshFilter>();
        _MeshRenderer = GetComponent<MeshRenderer>();
        
        _Mesh = new Mesh();
        _Mesh.name = "Cable Mesh";
        // Mesh is updated every frame; hint Unity to optimize for dynamic updates
        _Mesh.MarkDynamic();
        _MeshFilter.mesh = _Mesh;
        
        _SphereStart = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        _SphereStart.name = "Cable Start Cap";
        _SphereStart.transform.SetParent(transform);
        
        _SphereEnd = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        _SphereEnd.name = "Cable End Cap";
        _SphereEnd.transform.SetParent(transform);
        
        Destroy(_SphereStart.GetComponent<Collider>());
        Destroy(_SphereEnd.GetComponent<Collider>());
    }
    
    void UpdateLOD()
    {
        if (!_UseLOD) 
        {
            _CurrentLODLevel = 0;
            return;
        }
        
        Camera cam = Camera.main;
        if (cam == null) return;
        
        float distance = Vector3.Distance(transform.position, cam.transform.position);
        
        if (distance < _LODDistance * 0.3f)
            _CurrentLODLevel = 0;
        else if (distance < _LODDistance * 0.7f)
            _CurrentLODLevel = 1;
        else if (distance < _LODDistance)
            _CurrentLODLevel = 2;
        else
            _CurrentLODLevel = 3;
    }

    void RenderCable()
    {
        if (_Points == null || _Points.Length < 2) return;
        
        Vector3[] positions = new Vector3[_Segments + 1];
        for (int i = 0; i <= _Segments; i++)
        {
            positions[i] = _Points[i].Position;
        }
        _Positions = positions;
        
        int sides = Mathf.Max(3, _Sides - _CurrentLODLevel * 2);
        GenerateMesh(sides);
        UpdateEndCaps();
    }
    
    void UpdateEndCaps()
    {
        if (_SphereStart != null && _SphereEnd != null)
        {
            _SphereStart.transform.position = _Positions[0];
            _SphereStart.transform.localScale = Vector3.one * _Radius * 2f;
            
            _SphereEnd.transform.position = _Positions[_Positions.Length - 1];
            _SphereEnd.transform.localScale = Vector3.one * _Radius * 2f;
            
            if (_MeshRenderer.sharedMaterial != null)
            {
                _SphereStart.GetComponent<MeshRenderer>().sharedMaterial = _MeshRenderer.sharedMaterial;
                _SphereEnd.GetComponent<MeshRenderer>().sharedMaterial = _MeshRenderer.sharedMaterial;
            }
        }
    }

    void GenerateMesh(int sides)
    {
        if (_Mesh == null || _Positions == null || _Positions.Length <= 1) return;
        
        int vertexCount = sides * _Positions.Length;
        
        if (_Vertices == null || _Vertices.Length != vertexCount)
        {
            _Vertices = new Vector3[vertexCount];
            _Normals = new Vector3[vertexCount];
            _Triangles = GenerateIndices(sides);
            _UVs = GenerateUVs(sides);
        }
        
        int vertIndex = 0;
        for (int i = 0; i < _Positions.Length; i++)
        {
            Vector3[] circle = CalculateCircle(i, sides);
            for (int j = 0; j < sides; j++)
            {
                _Vertices[vertIndex] = transform.InverseTransformPoint(circle[j]);
                vertIndex++;
            }
        }
        
        _Mesh.Clear();
        _Mesh.vertices = _Vertices;
        _Mesh.triangles = _Triangles;
        _Mesh.uv = _UVs;
        _Mesh.RecalculateNormals();
        _Mesh.RecalculateBounds();
        
        _MeshFilter.mesh = _Mesh;
    }

    Vector2[] GenerateUVs(int sides)
    {
        var uvs = new Vector2[_Positions.Length * sides];
        
        for (int segment = 0; segment < _Positions.Length; segment++)
        {
            for (int side = 0; side < sides; side++)
            {
                int vertIndex = segment * sides + side;
                float u = (float)side / (sides - 1);
                float v = (float)segment / (_Positions.Length - 1);
                uvs[vertIndex] = new Vector2(u, v);
            }
        }
        
        return uvs;
    }

    int[] GenerateIndices(int sides)
    {
        int triCount = (_Positions.Length - 1) * sides * 2 * 3;
        int[] indices = new int[triCount];
        
        int idx = 0;
        for (int segment = 1; segment < _Positions.Length; segment++)
        {
            for (int side = 0; side < sides; side++)
            {
                int current = segment * sides + side;
                int previous = current - sides;
                int nextSide = (side == sides - 1) ? (current - (sides - 1)) : (current + 1);
                int prevNextSide = (side == sides - 1) ? (previous - (sides - 1)) : (previous + 1);
                
                indices[idx++] = previous;
                indices[idx++] = nextSide;
                indices[idx++] = current;
                
                indices[idx++] = prevNextSide;
                indices[idx++] = nextSide;
                indices[idx++] = previous;
            }
        }
        
        return indices;
    }

    Vector3[] CalculateCircle(int index, int sides)
    {
        Vector3 forward = Vector3.zero;
        int dirCount = 0;
        
        if (index > 0)
        {
            forward += (_Positions[index] - _Positions[index - 1]).normalized;
            dirCount++;
        }
        
        if (index < _Positions.Length - 1)
        {
            forward += (_Positions[index + 1] - _Positions[index]).normalized;
            dirCount++;
        }
        
        if (dirCount > 0)
            forward = (forward / dirCount).normalized;
        else
            forward = Vector3.forward;
        
        Vector3 up = Vector3.up;
        if (Mathf.Abs(Vector3.Dot(forward, up)) > 0.99f)
            up = Vector3.right;
        
        Vector3 side = Vector3.Cross(forward, up).normalized;
        up = Vector3.Cross(forward, side).normalized;
        
        Vector3[] circle = new Vector3[sides];
        float angleStep = (2f * Mathf.PI) / sides;
        
        for (int i = 0; i < sides; i++)
        {
            float angle = i * angleStep;
            float x = Mathf.Cos(angle) * _Radius;
            float y = Mathf.Sin(angle) * _Radius;
            circle[i] = _Positions[index] + side * x + up * y;
        }
        
        return circle;
    }
    
    void DebugDrawCollisions()
    {
        if (_Points == null) return;
        
        for (int i = 0; i <= _Segments; i++)
        {
            if (_Points[i].IsColliding())
            {
                Debug.DrawRay(_Points[i].Position, 
                    _Points[i].GetCollisionNormal() * 0.2f, 
                    Color.red);
            }
        }
    }

    #endregion
    
    #region PUBLIC API
    
    public Vector3 GetPointPosition(int index)
    {
        if (_Points != null && index >= 0 && index < _Points.Length)
            return _Points[index].Position;
        return Vector3.zero;
    }
    
    public void SetPointPosition(int index, Vector3 position)
    {
        if (_Points != null && index >= 0 && index < _Points.Length)
            _Points[index].Position = position;
    }
    
    public void ApplyImpulse(Vector3 impulse)
    {
        if (_Points == null) return;
        
        for (int i = 0; i <= _Segments; i++)
        {
            if (_Points[i].IsFree())
            {
                _Points[i].ApplyForce(impulse);
            }
        }
    }
    
    public void Reset()
    {
        InitializePhysics();
        if (_EnableCollisions)
        {
            if (_CollisionContainer != null)
            {
                if (Application.isPlaying) Destroy(_CollisionContainer); else DestroyImmediate(_CollisionContainer);
            }
            InitializeCollisionBodies();
        }
        _IsBroken = false;
        _MaxStretchRatio = 0f;
    }

    // Force break the rope programmatically
    public void ForceBreak()
    {
        if (!_IsBroken)
        {
            BreakRope((_Points[0].Position + _Points[_Segments].Position) * 0.5f);
        }
    }

    // Get current stretch ratio
    public float GetCurrentStretchRatio()
    {
        if (_Points == null || _Points.Length < 2) return 0f;
        return Vector3.Distance(_Points[0].Position, _Points[_Segments].Position) / _CableLength;
    }

    #endregion
}
