using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Component that monitors machines and displays their status on a ScreenDisplay
/// Can be attached to any GameObject - will find ScreenDisplay automatically or via assignment
/// </summary>
    public class MachineMonitor : MonoBehaviour
    {
        [Header("Identity & Power")]
        [Tooltip("Stable unique ID for this monitor (auto-generated if empty)")]
        [SerializeField] private string monitorId = "";
    [Tooltip("If true, the monitor's screen requires power from the grid")] 
    [SerializeField] private bool requiresPower = true;
    [Tooltip("How much power this monitor consumes when on (for budgeting only)")]
    [SerializeField] private float powerConsumption = 0f;
    private bool isPowered = false;
    [Header("Monitoring Settings")]
    [SerializeField] private List<Machine> monitoredMachines = new List<Machine>();
    [SerializeField] private float updateInterval = 5f; // How often to refresh display (seconds)
    [SerializeField] private bool autoDiscoverMachines = true;
    [SerializeField] private float autoDiscoverRange = 50f;

    [Header("Display Settings")]
    [SerializeField] private bool showMachineList = true;
    [SerializeField] private bool showPowerStatus = true;
    [SerializeField] private bool showProductionStats = true;
    [SerializeField] private int maxMachinesToShow = 5;

    [Header("Screen Display Reference")]
    [Tooltip("Assign the ScreenDisplay component to show status on. If not assigned, will auto-find in children.")]
    [SerializeField] private ScreenDisplay screenDisplay;

    private float lastUpdateTime = 0f;
    private string lastDisplayText = "";
    private Dictionary<string, Machine> machineRegistry = new Dictionary<string, Machine>();

    public string MonitorId => monitorId;
    public bool RequiresPower => requiresPower;
    public float PowerConsumption => Mathf.Max(0f, powerConsumption);
    public bool IsPowered => isPowered;

        private void Awake()
        {
            // Ensure we have a stable ID
            if (string.IsNullOrEmpty(monitorId))
            {
                // Use GameObject name by default for stability across sessions
                monitorId = gameObject.name;
            }

        // Try to find ScreenDisplay in multiple ways
        if (screenDisplay == null)
        {
            // First, try to get it from this GameObject
            screenDisplay = GetComponent<ScreenDisplay>();

            // If not found, try to find it in children
            if (screenDisplay == null)
            {
                screenDisplay = GetComponentInChildren<ScreenDisplay>();
            }

            // If still not found, try to find it in parent
            if (screenDisplay == null)
            {
                screenDisplay = GetComponentInParent<ScreenDisplay>();
            }

            // If still not found, try to find any ScreenDisplay in the scene (last resort)
            if (screenDisplay == null)
            {
                screenDisplay = FindFirstObjectByType<ScreenDisplay>();
                if (screenDisplay != null)
                {
                    Debug.LogWarning($"MachineMonitor on {gameObject.name} auto-found ScreenDisplay on {screenDisplay.gameObject.name}. Consider assigning it manually for better performance.");
                }
            }
        }

        if (screenDisplay == null)
        {
            Debug.LogError($"[MachineMonitor] On {gameObject.name}: cannot find a ScreenDisplay component! Please assign one in the inspector.");
            enabled = false;
            return;
        }
        else
        {
            Debug.Log($"[MachineMonitor] On {gameObject.name}: found ScreenDisplay on {screenDisplay.gameObject.name}");
        }
    }

    private void Start()
    {
        if (autoDiscoverMachines)
        {
            DiscoverNearbyMachines();

            // Also try again after a short delay in case machines weren't registered yet
            StartCoroutine(DelayedDiscovery());
        }

        UpdateDisplay();

        // Wire up screen button events if available
        try
        {
            if (screenDisplay != null)
            {
                screenDisplay.AddWithdrawListener(OnWithdrawClick);
                screenDisplay.AddDiagnosticsListener(OnDiagnosticsClick);
                screenDisplay.AddShutdownListener(OnShutdownClick);
            }
        }
        catch { }
    }

    private void OnEnable()
    {
        PowerSystem.Instance?.RegisterMonitor(this);
        // Ensure screen reflects current power without disabling it
        try { screenDisplay?.SetPowered(isPowered); } catch { }
    }

    private void OnDisable()
    {
        PowerSystem.Instance?.UnregisterMonitor(this);
    }

    private System.Collections.IEnumerator DelayedDiscovery()
    {
        yield return new WaitForSeconds(1f); // Wait 1 second for machines to register
        int machinesBefore = monitoredMachines.Count;
        DiscoverNearbyMachines();
        // Only log if we found additional machines
        if (monitoredMachines.Count > machinesBefore)
        {
            Debug.Log($"[MachineMonitor] Delayed discovery found {monitoredMachines.Count - machinesBefore} additional machines");
        }
    }

    private void Update()
    {
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdateDisplay();
            lastUpdateTime = Time.time;
        }
    }

    public void SetPowerState(bool powered)
    {
        if (isPowered == powered) return;
        isPowered = powered;
        try { screenDisplay?.SetPowered(powered); } catch { }
    }

    public void AddMachine(Machine machine)
    {
        if (machine != null && !monitoredMachines.Contains(machine))
        {
            monitoredMachines.Add(machine);
            machineRegistry[machine.MachineId] = machine;
            Debug.Log($"[MachineMonitor] Now monitoring: {machine.MachineName}");
            UpdateDisplay();
        }
    }

    public void RemoveMachine(Machine machine)
    {
        if (machine != null && monitoredMachines.Contains(machine))
        {
            monitoredMachines.Remove(machine);
            machineRegistry.Remove(machine.MachineId);
            UpdateDisplay();
        }
    }

    public void DiscoverNearbyMachines()
    {
        if (PowerSystem.Instance != null)
        {
            var allMachines = PowerSystem.Instance.GetAllMachines();
            int machinesFound = 0;

            foreach (Machine machine in allMachines)
            {
                if (machine != null)
                {
                    float distance = Vector3.Distance(transform.position, machine.transform.position);

                    if (distance <= autoDiscoverRange && !monitoredMachines.Contains(machine))
                    {
                        AddMachine(machine);
                        machinesFound++;
                    }
                }
            }

            if (machinesFound > 0)
            {
                Debug.Log($"[MachineMonitor] Auto-discovered {machinesFound} machines. Total monitoring: {monitoredMachines.Count}");
            }
        }
        else
        {
            Debug.LogError("[MachineMonitor] PowerSystem.Instance is null!");
        }
    }

    private void UpdateDisplay()
    {
        if (screenDisplay == null)
        {
            Debug.LogWarning("[MachineMonitor] ScreenDisplay is null, cannot update display");
            return;
        }

        // Check if the screen is actually visible (not distance culled)
        if (!IsScreenVisible())
        {
            return; // Don't update or log when screen is not visible
        }

        string displayText = GenerateDisplayText();

        // Only log when we actually have machines or when the display changes significantly
        if (monitoredMachines.Count > 0 || lastDisplayText != displayText)
        {
            Debug.Log($"[MachineMonitor] Updating display with {monitoredMachines.Count} machines");
            lastDisplayText = displayText;
        }

        screenDisplay.SetPlaceholder(displayText);
    }

    /// <summary>
    /// Check if the screen is actually visible (not distance culled)
    /// </summary>
    private bool IsScreenVisible()
    {
        if (screenDisplay == null) return false;

        // Check if the screen's canvas is active (not distance culled)
        // ScreenDisplay inherits from InteractiveWorldScreen which has a worldCanvas
        var canvas = screenDisplay.GetComponentInChildren<Canvas>();
        return canvas != null && canvas.gameObject.activeInHierarchy;
    }

    private string GenerateDisplayText()
    {
        if (monitoredMachines.Count == 0)
        {
            return @"NO MACHINES DETECTED
----------------------------------------
< UNIT: DISCONNECTED >
< STATUS: OFFLINE                     >
----------------------------------------
SYS INTEGRITY: [░░░░░░░░░░░░░░░░░░] 0.0%
THERMAL:       --°C
POWER DRAW:    0.00 kW
EFFICIENCY:    0.0%

YIELD BUFFER:  [0.00] credits

----------------------------------------";
        }

        // Show the first machine in detail (you can cycle through multiple machines)
        Machine primaryMachine = monitoredMachines[0];
        if (primaryMachine == null)
        {
            return "MACHINE ERROR\n";
        }

        // Generate unit ID (use machine name or generate from type)
        string unitId = GenerateUnitId(primaryMachine);

        // Status based on machine state
        string status = GetMachineStatus(primaryMachine);

        // System integrity (durability)
        float integrityPercent = primaryMachine.DurabilityPercentage * 100f;
        string integrityBar = GenerateProgressBar(primaryMachine.DurabilityPercentage, 14);

        // Thermal (fake temperature based on state)
        string thermal = GenerateThermalReading(primaryMachine);

        // Power draw
        float powerDraw = primaryMachine.PowerConsumption;
        string powerDrawStr = $"{powerDraw:F2}";

        // Efficiency
        float efficiency = 0f;
        if (primaryMachine is ServerMachine server)
        {
            efficiency = server.CurrentMiningEfficiency * 100f;
        }
        else
        {
            efficiency = primaryMachine.Efficiency * 100f;
        }

        // Yield buffer (currency/resources)
        string yieldBuffer = "0.00";
        if (primaryMachine is ServerMachine serverMachine)
        {
            yieldBuffer = serverMachine.StoredCurrency.ToString("F2");
        }
        else if (primaryMachine is FactoryMachine factoryMachine)
        {
            yieldBuffer = factoryMachine.CurrentOutputStored.ToString("F2");
        }

        return $@"{unitId}
----------------------------------------
< UNIT: {unitId} >
< STATUS: {status} >
----------------------------------------
SYS INTEGRITY: [{integrityBar}] {integrityPercent:F1}%
THERMAL:       {thermal}°C
POWER DRAW:    {powerDrawStr} kW
EFFICIENCY:    {efficiency:F1}%

YIELD BUFFER:  [{yieldBuffer}] credits

----------------------------------------";
    }

    private string GenerateUnitId(Machine machine)
    {
        string prefix = machine.MachineType.ToString().ToUpper();
        return $"{prefix}-{machine.MachineId.Substring(0, Mathf.Min(7, machine.MachineId.Length))}";

    }

    private string GetMachineStatus(Machine machine)
    {
        if (!machine.IsPowered)
            return "OFFLINE                    ";
        if (machine.CurrentState == MachineState.Running)
            return "ONLINE / PROCESSING        ";
        if (machine.CurrentState == MachineState.Maintenance)
            return "MAINTENANCE REQUIRED       ";
        if (machine.CurrentState == MachineState.Broken)
            return "SYSTEM FAILURE             ";
        return "IDLE                       ";
    }

    private string GenerateProgressBar(float percentage, int length)
    {
        int filled = Mathf.RoundToInt(percentage * length);
        string bar = "";

        for (int i = 0; i < length; i++)
        {
            bar += (i < filled) ? "█" : "░";
        }

        return bar;
    }

    private string GenerateThermalReading(Machine machine)
    {
        if (!machine.IsPowered)
            return "--";

        // Base temperature
        float baseTemp = 25f;

        // Add heat based on state
        if (machine.CurrentState == MachineState.Running)
            baseTemp += 45f; // Running machines are hotter
        if (machine.CurrentState == MachineState.Broken)
            baseTemp += 20f; // Broken machines can overheat

        // Add some randomness for realism
        baseTemp += Random.Range(-2f, 2f);

        return Mathf.RoundToInt(baseTemp).ToString();
    }

    // ScreenDisplay integration - handle button clicks
    public void OnWithdrawClick()
    {
        // Collect currency/resources from all machines
        int totalCollected = 0;
        foreach (Machine machine in monitoredMachines)
        {
            if (machine != null)
            {
                if (machine is ServerMachine server)
                {
                    totalCollected += server.CollectCurrency();
                }
                else if (machine is FactoryMachine factory)
                {
                    factory.CollectAllOutput();
                }
            }
        }
        UpdateDisplay();
        if (totalCollected > 0)
        {
            // Show simple "+00042" style notification
            try
            {
                NotificationManager.Instance?.ShowNotification($"+{totalCollected:00000}");
            }
            catch { }
        }
    }

    public void OnDiagnosticsClick()
    {
        // Show detailed diagnostics
        string diagnostics = "DIAGNOSTICS\n===========\n\n";

        foreach (Machine machine in monitoredMachines)
        {
            if (machine != null)
            {
                diagnostics += machine.GetMachineStatus() + "\n";
                diagnostics += "----------------\n";
            }
        }

        screenDisplay.SetPlaceholder(diagnostics);
    }

    public void OnShutdownClick()
    {
        // Emergency shutdown all machines
        foreach (Machine machine in monitoredMachines)
        {
            if (machine != null)
            {
                machine.StopMachine();
            }
        }
        UpdateDisplay();
    }

    // Public API
    public List<Machine> GetMonitoredMachines()
    {
        return new List<Machine>(monitoredMachines);
    }

    public void ClearAllMachines()
    {
        monitoredMachines.Clear();
        machineRegistry.Clear();
        UpdateDisplay();
    }

    public void RefreshDisplay()
    {
        UpdateDisplay();
    }
}

