using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Tool for creating cable connections between machines, generators, and monitors
/// Monitors can be connected to observe specific machines via cable
/// </summary>
public class PowerWiringTool : MonoBehaviour
{
    [Header("Wiring Settings")]
    [SerializeField] private LayerMask connectableLayers = -1;
    [SerializeField] private float maxConnectionDistance = 50f;

    [Header("Camera Settings")]
    [SerializeField] private Camera playerCamera;

    [Header("Physical Cable Settings")]
    [SerializeField] private Material cableMaterial;
    [SerializeField] private float cableRadius = 0.02f;
    [SerializeField] private float cableMass = 0.1f;
    [SerializeField] private float cableStiffness = 0.8f;
    [SerializeField] private bool enableCableCollisions = true;
    [SerializeField] private LayerMask cableCollisionLayers = -1;

    [Header("Item Consumption")]
    [SerializeField] private bool requireRopeItemToPlace = true;
    [SerializeField] private string ropeItemName = "Rope";

    [Header("Preview Line")]
    [SerializeField] private Material previewLineMaterial;
    [SerializeField] private float previewLineWidth = 0.06f;
    [SerializeField] private Color validPlacementColor = new Color(0.2f, 1f, 0.2f, 0.95f);
    [SerializeField] private Color invalidPlacementColor = new Color(1f, 0.2f, 0.2f, 0.95f);
    [SerializeField] private bool showCatenaryPreview = true;
    [SerializeField] private float slackMultiplier = 1.2f; // should match CreateCable slack

    private Transform firstPoint;
    private Component firstComponent; // Could be Machine, Generator, or MachineMonitor
    private List<GameObject> createdCables = new List<GameObject>();
    private LineRenderer previewLine;
    private bool isPlacementValid = false;

    private void Start()
    {
        if (!enabled) return;

        if (playerCamera == null)
        {
            playerCamera = Camera.main;
            if (playerCamera == null)
            {
                playerCamera = FindFirstObjectByType<Camera>();
            }
        }

        CreatePreviewLine();
    }

    private void OnEnable()
    {
        if (playerCamera == null)
        {
            playerCamera = Camera.main;
            if (playerCamera == null)
            {
                playerCamera = FindFirstObjectByType<Camera>();
            }
        }

        Debug.Log("Power Wiring Tool enabled - Press E to select/connect machines/generators/monitors");
    }

    private void OnDisable()
    {
        CancelConnection();
        CleanupCables();
        Debug.Log("Power Wiring Tool disabled");
    }

    private void Update()
    {
        if (!enabled) return;
        HandleInput();
        UpdatePreviewLine();
    }

    private void HandleInput()
    {
        // E key to select/connect nodes
        if (Input.GetKeyDown(KeyCode.E))
        {
            TryConnect();
        }

        // Right click or Escape to cancel selection
        if ((Input.GetKeyDown(KeyCode.Escape) || Input.GetMouseButtonDown(1)) && firstPoint != null)
        {
            CancelConnection();
        }
    }

    private void TryConnect()
    {
        Ray ray = playerCamera.ScreenPointToRay(new Vector3(Screen.width/2, Screen.height/2, 0));

        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Max(maxConnectionDistance, 200f), connectableLayers, QueryTriggerInteraction.Ignore))
        {
            // Check what we hit - machine, generator, or monitor
            Machine machine = hit.collider.GetComponentInParent<Machine>();
            Generator generator = hit.collider.GetComponentInParent<Generator>();
            MachineMonitor monitor = hit.collider.GetComponentInParent<MachineMonitor>();

            // Any of these components can be connected
            Component hitComponent = (Component)machine ?? (Component)generator ?? monitor;

            if (hitComponent != null)
            {
                if (firstPoint == null)
                {
                    // First selection
                    firstPoint = hit.transform;
                    firstComponent = hitComponent;
                    
                    string componentType = GetComponentTypeName(hitComponent);
                    Debug.Log($"Selected {componentType}: {hitComponent.gameObject.name}");
                }
                else if (firstPoint == hit.transform)
                {
                    // Clicked same object, deselect
                    CancelConnection();
                }
                else
                {
                    // Second selection - check if connection is valid
                    Component secondComponent = (Component)machine ?? (Component)generator ?? monitor;
                    
                    if (IsValidConnection(firstComponent, secondComponent))
                    {
                        if (requireRopeItemToPlace && !ConsumeRopeFromInventory())
                        {
                            Debug.LogWarning("Need 1 Rope in inventory to place a cable.");
                            CancelConnection();
                            return;
                        }
                        // Create cable
                        CreateCable(firstPoint.position, hit.point, firstPoint, hit.transform);
                        
                        // Handle monitor connections specially
                        HandleMonitorConnection(firstComponent, secondComponent);
                        
                        // Reset selection
                        firstPoint = null;
                        firstComponent = null;
                    }
                    else
                    {
                        Debug.LogWarning("Invalid connection: Monitors should connect to machines or generators, not to each other.");
                        CancelConnection();
                    }
                }
            }
        }
    }

    private void CreatePreviewLine()
    {
        GameObject previewObj = new GameObject("PowerWirePreview");
        previewObj.transform.SetParent(transform);

        previewLine = previewObj.AddComponent<LineRenderer>();

        if (previewLineMaterial == null)
        {
            // Fallback material similar to RopeClickPlacer
            previewLineMaterial = new Material(Shader.Find("Sprites/Default"));
        }

        previewLine.material = previewLineMaterial;
        previewLine.startWidth = previewLineWidth;
        previewLine.endWidth = previewLineWidth;
        previewLine.positionCount = 2;
        previewLine.enabled = false;
        previewLine.sortingOrder = 100;
    }

    private void UpdatePreviewLine()
    {
        if (firstPoint != null && previewLine != null)
        {
            // Raycast from screen center to find prospective second node
            Vector3 screenCenter = new Vector3(Screen.width / 2f, Screen.height / 2f, 0f);
            Ray ray = playerCamera.ScreenPointToRay(screenCenter);

            bool hasHit = Physics.Raycast(ray, out RaycastHit hit, Mathf.Max(maxConnectionDistance, 200f), connectableLayers, QueryTriggerInteraction.Ignore);
            Vector3 currentPos = hasHit ? hit.point : GetFallbackPosition();

            // Determine validity
            isPlacementValid = false;
            if (hasHit)
            {
                // Resolve types analogous to TryConnect
                Machine machine = hit.collider.GetComponentInParent<Machine>();
                Generator generator = hit.collider.GetComponentInParent<Generator>();
                MachineMonitor monitor = hit.collider.GetComponentInParent<MachineMonitor>();
                Component secondComponent = (Component)machine ?? (Component)generator ?? monitor;

                bool typeValid = (secondComponent != null) && IsValidConnection(firstComponent, secondComponent) && hit.transform != firstPoint;
                bool distValid = Vector3.Distance(firstPoint.position, hit.point) <= maxConnectionDistance;
                isPlacementValid = typeValid && distValid;
            }

            // Draw preview (straight or catenary curve)
            if (showCatenaryPreview)
            {
                float distance = Vector3.Distance(firstPoint.position, currentPos);
                UpdateCatenaryPreview(firstPoint.position, currentPos, distance * slackMultiplier);
            }
            else
            {
                previewLine.positionCount = 2;
                previewLine.SetPosition(0, firstPoint.position);
                previewLine.SetPosition(1, currentPos);
            }

            // Colorize based on validity
            Color previewColor = isPlacementValid ? validPlacementColor : invalidPlacementColor;
            previewLine.startColor = previewColor;
            previewLine.endColor = previewColor;
            previewLine.enabled = true;
        }
        else
        {
            if (previewLine != null)
                previewLine.enabled = false;
        }
    }

    private void UpdateCatenaryPreview(Vector3 start, Vector3 end, float length)
    {
        // Simple sag preview, consistent with RopeClickPlacer
        int curvePoints = 10;
        previewLine.positionCount = curvePoints;

        Vector3 delta = end - start;
        float sag = Mathf.Max(0f, (length - delta.magnitude) * 0.5f);

        for (int i = 0; i < curvePoints; i++)
        {
            float t = (float)i / (curvePoints - 1);
            Vector3 point = Vector3.Lerp(start, end, t);

            // Add sag
            float sagCurve = 4f * t * (1f - t);
            point.y -= sagCurve * sag;

            previewLine.SetPosition(i, point);
        }
    }

    private Vector3 GetFallbackPosition()
    {
        // Place point in front of camera if no surface hit
        return playerCamera.transform.position + playerCamera.transform.forward * (maxConnectionDistance * 0.5f);
    }

    private string GetComponentTypeName(Component component)
    {
        if (component is Machine) return "Machine";
        if (component is Generator) return "Generator";
        if (component is MachineMonitor) return "Monitor";
        return "Unknown";
    }

    private bool IsValidConnection(Component first, Component second)
    {
        // Don't allow monitor-to-monitor connections
        if (first is MachineMonitor && second is MachineMonitor)
        {
            return false;
        }

        // All other combinations are valid:
        // - Machine to Machine (power transfer)
        // - Machine to Generator (power supply)
        // - Generator to Generator (grid connection)
        // - Monitor to Machine (monitoring connection)
        // - Monitor to Generator (monitoring connection)
        return true;
    }

    private void HandleMonitorConnection(Component first, Component second)
    {
        // Check if either component is a monitor
        MachineMonitor monitor = null;
        Machine connectedMachine = null;
        Generator connectedGenerator = null;

        if (first is MachineMonitor)
        {
            monitor = first as MachineMonitor;
            connectedMachine = second as Machine;
            connectedGenerator = second as Generator;
        }
        else if (second is MachineMonitor)
        {
            monitor = second as MachineMonitor;
            connectedMachine = first as Machine;
            connectedGenerator = first as Generator;
        }

        // If we have a monitor connection, add the machine/generator to its monitoring list
        if (monitor != null)
        {
            if (connectedMachine != null)
            {
                monitor.AddMachine(connectedMachine);
                Debug.Log($"Connected {connectedMachine.MachineName} to monitor {monitor.gameObject.name} for monitoring");
            }
            else if (connectedGenerator != null)
            {
                // Monitors might want to track generators too for power statistics
                Debug.Log($"Connected generator {connectedGenerator.GeneratorName} to monitor {monitor.gameObject.name}");
                // Note: MachineMonitor doesn't have AddGenerator method yet, 
                // but the cable connection is still valid for visual purposes
            }
        }
    }

    private void CreateCable(Vector3 start, Vector3 end, Transform startAttach, Transform endAttach)
    {
        // Create cable object
        GameObject cableObj = new GameObject("PowerCable");
        createdCables.Add(cableObj);

        // Add cable component
        CableComponent cable = cableObj.AddComponent<CableComponent>();
        if (cable == null)
        {
            Debug.LogError("Failed to add CableComponent to power cable!");
            return;
        }

        // Add RopeStaticMeshCapture for capture and persistence
        RopeStaticMeshCapture captureComponent = cableObj.AddComponent<RopeStaticMeshCapture>();

        // Listen for rope breaking events
        cable.OnRopeBreak += HandleCableBreak;

        // Create anchor points
        GameObject startAnchor = CreateCableAnchor("StartAnchor", start, startAttach);
        GameObject endAnchor = CreateCableAnchor("EndAnchor", end, endAttach);

        // Configure cable
        cable.StartPoint = startAnchor.transform;
        cable.EndPoint = endAnchor.transform;
        cable.CableLength = Vector3.Distance(start, end) * 1.2f; // 20% slack

        // Apply physics settings
        ConfigureCablePhysics(cable);

        // Set material
        if (cableMaterial != null)
        {
            MeshRenderer meshRenderer = cableObj.GetComponent<MeshRenderer>();
            if (meshRenderer != null)
                meshRenderer.sharedMaterial = cableMaterial;
        }

        // Register all cables with the power system (monitors participate in the grid)
        PowerSystem.Instance.AddCableConnection(cableObj, startAttach, endAttach);
        Debug.Log("Created cable (registered with PowerSystem)");

        // Initialize cable physics
        cable.ReinitializeCable();

        // Configure auto-freeze after idle and brief stabilization
        var autoFreeze = cableObj.AddComponent<RopeAutoFreeze>();
        cableObj.AddComponent<CableStabilizer>();

        // Build a persistent key for this cable using canonical, prefixed endpoint IDs if available
        string idA = GetEndpointKey(startAttach);
        string idB = GetEndpointKey(endAttach);
        if (!string.IsNullOrEmpty(idA) && !string.IsNullOrEmpty(idB))
        {
            // Canonical ordering for stability
            string a = idA, b = idB;
            if (string.CompareOrdinal(a, b) > 0) { var tmp = a; a = b; b = tmp; }
            string prefsKey = $"CableState|{a}|{b}";

            // Set custom prefs key so capture saves under deterministic key
            captureComponent.SetCustomPrefsKey(prefsKey);
        }

        Debug.Log($"Cable created!");
    }

    private string GetEndpointKey(Transform attach)
    {
        if (attach == null) return string.Empty;
        var m = attach.GetComponentInParent<Machine>();
        if (m != null) return $"M:{m.MachineId}";
        var g = attach.GetComponentInParent<Generator>();
        if (g != null) return $"G:{g.GeneratorId}";
        var mon = attach.GetComponentInParent<MachineMonitor>();
        if (mon != null) return $"MONID:{mon.MonitorId}";
        // Fallback to name when no known endpoint found; prefix with N: to avoid collisions
        return $"N:{attach.gameObject.name}";
    }

    private GameObject CreateCableAnchor(string name, Vector3 position, Transform attachment)
    {
        GameObject anchor = new GameObject(name);
        anchor.transform.position = position;

        if (attachment != null)
        {
            // Check if attachment has a rigidbody
            Rigidbody rb = attachment.GetComponent<Rigidbody>();
            if (rb != null)
            {
                // Make anchor a child of the rigidbody for proper physics
                anchor.transform.SetParent(attachment);
            }
            else
            {
                // Just position it at the attachment point
                anchor.transform.position = position;

                // Parent to static geometry if it's not the player
                if (!attachment.CompareTag("Player"))
                {
                    anchor.transform.SetParent(attachment);
                }
            }
        }

        return anchor;
    }

    private void ConfigureCablePhysics(CableComponent cable)
    {
        // Apply physics settings via adapter (fewer segments for power cables)
        CableAdapter.TrySetTotalSegments(cable, 12);
        CableAdapter.TrySetRadius(cable, cableRadius);
        CableAdapter.TrySetMass(cable, cableMass);
        CableAdapter.TrySetStiffness(cable, cableStiffness);

        cable.EnableCollisions = enableCableCollisions;
        cable.CollisionLayers = cableCollisionLayers;
    }

    private void HandleCableBreak(CableComponent brokenCable, Vector3 breakPoint)
    {
        // Remove the broken cable from our tracking list
        if (createdCables.Contains(brokenCable.gameObject))
        {
            createdCables.Remove(brokenCable.gameObject);

            // Notify power system if it was a power cable
            // (Monitor cables aren't registered with PowerSystem)
            PowerSystem.Instance.RemoveCableConnection(brokenCable.gameObject);

            // Prevent any static persistence from being created after break
            var autoFreeze = brokenCable.GetComponent<RopeAutoFreeze>();
            if (autoFreeze != null) autoFreeze.enabled = false;
            var capture = brokenCable.GetComponent<RopeStaticMeshCapture>();
            if (capture != null)
            {
                capture.ClearSavedState();
                capture.enabled = false;
            }

            // Only drop rope if there's no RopeReactivationWatcher (to prevent double drops)
            var watcher = brokenCable.GetComponent<RopeReactivationWatcher>();
            if (watcher == null)
            {
                try
                {
                    var item = ItemDatabase.GetItemByName("Rope");
                    var dropper = GameObject.FindFirstObjectByType<InvItemDropping>();
                    if (item != null && dropper != null)
                    {
                        var obj = dropper.DropItem(item, 1);
                        if (obj != null)
                        {
                            obj.transform.position = breakPoint;
                            var rb = obj.GetComponent<Rigidbody>();
                            if (rb != null)
                            {
                                rb.linearVelocity = Vector3.zero;
                                rb.angularVelocity = Vector3.zero;
                            }
                        }
                    }
                }
                catch { }
            }

            // Immediately hide any remaining visuals to avoid a frozen rope ghost
            try
            {
                var rends = brokenCable.GetComponentsInChildren<Renderer>(true);
                foreach (var r in rends) if (r != null) r.enabled = false;
                var cols = brokenCable.GetComponentsInChildren<Collider>(true);
                foreach (var c in cols) if (c != null) c.enabled = false;
            }
            catch { }

            // Destroy the rope object now that it's broken and deregistered
            try { Destroy(brokenCable.gameObject); } catch { }

            Debug.Log($"Cable broke and was removed. {createdCables.Count} cables remaining.");
        }
    }

    private bool ConsumeRopeFromInventory()
    {
        try
        {
            var eq = FindObjectOfType<EquipmentManager>();
            if (eq == null) return false;
            var bagSlot = eq.GetEquipmentSlot(EquipmentSlotType.BagSlot);
            if (bagSlot?.storageContainer == null) return false;

            Item ropeItem = ItemDatabase.GetItemByName(ropeItemName);
            if (ropeItem == null) return false;

            // Find a stack with Rope and remove 1
            var stacks = bagSlot.storageContainer.GetItems();
            foreach (var stack in stacks)
            {
                if (stack?.Item == ropeItem && stack.Quantity > 0)
                {
                    bagSlot.storageContainer.RemoveItemStack(stack, 1);
                    return true;
                }
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    private void CancelConnection()
    {
        firstPoint = null;
        firstComponent = null;
        if (previewLine != null) previewLine.enabled = false;
        Debug.Log("Cable connection cancelled");
    }

    private void CleanupCables()
    {
        foreach (GameObject cable in createdCables)
        {
            if (cable != null)
            {
                PowerSystem.Instance.RemoveCableConnection(cable);
                Destroy(cable);
            }
        }
        createdCables.Clear();
        Debug.Log("Cleaned up all cables");
    }

    // Public API for external control
    public void EnableWiring()
    {
        this.enabled = true;
    }

    public void DisableWiring()
    {
        CancelConnection();
        this.enabled = false;
    }

    public bool IsSelectingNode()
    {
        return firstPoint != null;
    }

    public void CancelCurrentSelection()
    {
        CancelConnection();
    }

    // Method called by ToolSelectionController when Cable tool is used
    public void SimulateToolUse()
    {
        if (!enabled)
        {
            Debug.LogWarning("PowerWiringTool is disabled - Cable tool may not be equipped properly");
            return;
        }

        // Simulate pressing E key
        TryConnect();
    }
}
